    <aside class="main-sidebar sidebar-dark-primary elevation-4" style="margin-top: 0; padding-top: 0;">
    <!-- Brand Logo -->
    <a href="<?= ADMIN_BASE_URL ?>pages/dashboard.php" class="brand-link text-truncate d-flex align-items-center">
      <img src="<?= ADMIN_BASE_URL ?>assets/images/timbook-carles-tourism.png" alt="Timbook Carles Tourism" style="height:42px; width:42px; object-fit:contain; margin-right:10px; background:#fff; border-radius:50%; border:1px solid #ccc;">
      <div style="min-width:0;">
        <span class="brand-text font-weight-light d-block" style="font-size: 1.15rem; font-weight: 500 !important;">Online Booking System</span>
        <span class="brand-text font-weight-light d-block text-muted" style="font-size: 1rem;">Carles Tourism</span>
      </div>
    </a>
    <div class="sidebar">
      <!-- Sidebar user panel (optional) -->
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <?php
          $utype = isset($_SESSION['utype']) ? $_SESSION['utype'] : 1;
          $main_admin_id = 1;
          $adminid = intval($_SESSION['aid']);
          if ($utype == 1) {
            // If main admin, show their own online status (always online)
            $online_status = 'online';
          } else {
            // Sub-admin: check main admin's last_activity
            $stmt = $con->prepare("SELECT last_activity FROM admins WHERE admin_id=?");
            $stmt->bind_param("i", $main_admin_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $admin_activity = $result->fetch_assoc();
            $stmt->close();
            $last_activity = isset($admin_activity['last_activity']) ? strtotime($admin_activity['last_activity']) : 0;
            $now = time();
            $online_status = ($now - $last_activity <= 300) ? 'online' : 'offline'; // 5 minutes
          }
        ?>
        <div class="image" style="position:relative;">
          <?php
            $adminid = intval($_SESSION['aid']);
            // Get profile image from persistent JSON map
            $profile_img = ADMIN_BASE_URL . 'pages/dist/img/admindefault.png';
            $profile_map_file = ADMIN_ROOT . 'uploads/profile_images/profile_images_map.json';
            if(file_exists($profile_map_file)) {
              $map = json_decode(file_get_contents($profile_map_file), true);
              if(isset($map[$adminid]) && file_exists(ADMIN_ROOT . 'uploads/profile_images/' . $map[$adminid])) {
                $profile_img = ADMIN_BASE_URL . 'uploads/profile_images/' . $map[$adminid];
              }
            }
            // Always fetch admin info for name display
            $stmt = $con->prepare("SELECT first_name, last_name FROM admins WHERE admin_id=?");
            $stmt->bind_param("i", $adminid);
            $stmt->execute();
            $result = $stmt->get_result()->fetch_assoc();
            $stmt->close();
          ?>
          <img src="<?php echo $profile_img; ?>" class="img-circle elevation-2" alt="Admin Image" style="width:42px; height:42px; object-fit:cover;">
        </div>
        <div class="info">
          <a href="<?= ADMIN_BASE_URL ?>pages/profile.php" class="d-block" style="font-size: 1.1rem; font-weight: 500;">
            <?php
            if (!empty($result['first_name']) || !empty($result['last_name'])) {
              echo htmlspecialchars(trim($result['first_name'] . ' ' . $result['last_name']));
            } else {
              echo 'Unknown User';
            }
            ?>
          </a>
          <span class="d-block text-muted" style="font-size: 0.9rem; line-height:1.2;">Administrator</span>
          <div class="status-container">
            <span id="statusIndicator" style="width: 10px; height: 10px;"></span>
            <span id="statusText" style="font-size:0.9rem;color:#28a745;transition: all 0.3s ease; font-weight: 500;">Online</span>
          </div>
        </div>
      </div>

      <!-- Sidebar Menu -->
      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->
          <li class="nav-item">
            <a href="<?= ADMIN_BASE_URL ?>pages/dashboard.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'dashboard.php') echo ' active'; ?>">
              <i class="nav-icon fas fa-home"></i>
              <p>Dashboard</p>
            </a>
          </li>

          <!-- Admin section moved to profile section -->

          <li class="nav-header" style="font-size: 1.1rem; padding: 1rem 1rem 0.5rem 1rem;">BOOKINGS</li>
          <li class="nav-item <?php if(in_array(basename($_SERVER['PHP_SELF']), ['booking-calendar.php', 'daily-schedule.php'])) echo 'menu-open'; ?>">
            <a href="#" class="nav-link <?php if(in_array(basename($_SERVER['PHP_SELF']), ['booking-calendar.php', 'daily-schedule.php'])) echo 'active'; ?>">
              <i class="nav-icon fas fa-calendar"></i>
              <p>
                Calendar & Schedule
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/booking-calendar.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'booking-calendar.php') echo ' active'; ?>">
                  <i class="fas fa-calendar-alt nav-icon"></i>
                  <p>Calendar View</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/daily-schedule.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'daily-schedule.php') echo ' active'; ?>">
                  <i class="fas fa-calendar-day nav-icon"></i>
                  <p>Daily Schedule</p>
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-item <?php if(in_array(basename($_SERVER['PHP_SELF']), ['pending-bookings.php', 'accepted-bookings.php', 'rejected-bookings.php', 'all-booking.php', 'passenger-manifest.php'])) echo 'menu-open'; ?>">
            <a href="#" class="nav-link <?php if(in_array(basename($_SERVER['PHP_SELF']), ['pending-bookings.php', 'accepted-bookings.php', 'rejected-bookings.php', 'all-booking.php', 'passenger-manifest.php'])) echo 'active'; ?>">
              <i class="nav-icon fas fa-clipboard-check"></i>
              <p>
                Booking Management
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/pending-bookings.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'pending-bookings.php') echo ' active'; ?>">
                  <i class="fas fa-clock nav-icon"></i>
                  <p>Pending Bookings</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/accepted-bookings.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'accepted-bookings.php') echo ' active'; ?>">
                  <i class="fas fa-check-circle nav-icon"></i>
                  <p>Accepted Bookings</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/rejected-bookings.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'rejected-bookings.php') echo ' active'; ?>">
                  <i class="fas fa-times-circle nav-icon"></i>
                  <p>Rejected Bookings</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/all-booking.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'all-booking.php') echo ' active'; ?>">
                  <i class="fas fa-list nav-icon"></i>
                  <p>All Bookings</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/passenger-manifest.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'passenger-manifest.php') echo ' active'; ?>">
                  <i class="fas fa-users nav-icon"></i>
                  <p>Passenger Manifest</p>
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-header" style="font-size: 1.1rem; padding: 1rem 1rem 0.5rem 1rem;">MANAGEMENT</li>
          <li class="nav-item <?php if(in_array(basename($_SERVER['PHP_SELF']), ['manage-boat.php', 'manage-boats.php', 'boat-assignment-lists.php'])) echo 'menu-open'; ?>">
            <a href="#" class="nav-link <?php if(in_array(basename($_SERVER['PHP_SELF']), ['manage-boat.php', 'manage-boats.php', 'boat-assignment-lists.php'])) echo 'active'; ?>">
              <i class="nav-icon fas fa-ship"></i>
              <p>
                Boat Management
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/manage-boat.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'manage-boat.php' || basename($_SERVER['PHP_SELF']) == 'manage-boats.php') echo ' active'; ?>">
                  <i class="fas fa-ship nav-icon"></i>
                  <p>Manage Boats</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/boat-assignment-lists.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'boat-assignment-lists.php') echo ' active'; ?>">
                  <i class="fas fa-clipboard-list nav-icon"></i>
                  <p>Boat Assignment Lists</p>
                </a>
              </li>
            </ul>
          </li>



          <li class="nav-header" style="font-size: 1.1rem; padding: 1rem 1rem 0.5rem 1rem;">RESERVATIONS</li>
          <li class="nav-item">
            <a href="<?= ADMIN_BASE_URL ?>pages/all-reservations.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'all-reservations.php') echo ' active'; ?>" onclick="event.preventDefault(); window.location.href='<?= ADMIN_BASE_URL ?>pages/all-reservations.php';">
              <i class="nav-icon fas fa-bookmark"></i>
              <p>All Reservations</p>
            </a>
          </li>

          <li class="nav-header" style="font-size: 1.1rem; padding: 1rem 1rem 0.5rem 1rem;">REPORTS</li>
          <li class="nav-item <?php if(in_array(basename($_SERVER['PHP_SELF']), ['bw-dates-report.php', 'tourist-report.php'])) echo 'menu-open'; ?>">
            <a href="#" class="nav-link <?php if(in_array(basename($_SERVER['PHP_SELF']), ['bw-dates-report.php', 'tourist-report.php'])) echo 'active'; ?>">
              <i class="nav-icon fas fa-chart-bar"></i>
              <p>
                Reports & Analytics
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/bw-dates-report.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'bw-dates-report.php') echo ' active'; ?>">
                  <i class="fas fa-chart-line nav-icon"></i>
                  <p>Bookings Report</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/tourist-report.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'tourist-report.php') echo ' active'; ?>">
                  <i class="fas fa-users nav-icon"></i>
                  <p>Tourist Report</p>
                </a>
              </li>

            </ul>
          </li>

          <li class="nav-header" style="font-size: 1.1rem; padding: 1rem 1rem 0.5rem 1rem;">PROFILE</li>
          <li class="nav-item <?php if(in_array(basename($_SERVER['PHP_SELF']), ['profile.php', 'change-password.php'])) echo 'menu-open'; ?>">
            <a href="#" class="nav-link <?php if(in_array(basename($_SERVER['PHP_SELF']), ['profile.php', 'change-password.php'])) echo 'active'; ?>">
              <i class="nav-icon fas fa-cogs"></i>
              <p>
               Account Settings
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/profile.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'profile.php') echo ' active'; ?>">
                  <i class="far fa-user nav-icon"></i>
                  <p>Profile</p>
                </a>
              </li>

              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/change-password.php" class="nav-link<?php if(basename($_SERVER['PHP_SELF']) == 'change-password.php') echo ' active'; ?>">
                  <i class="fas fa-cog nav-icon"></i>
                  <p>Change Password</p>
                </a>
              </li>



              <?php /* Sub-admin management removed */ ?>

              <li class="nav-item">
                <a href="<?= ADMIN_BASE_URL ?>pages/logout.php" class="nav-link" id="logoutBtn">
                  <i class="fas fa-sign-out-alt nav-icon"></i>
                  <p>Logout</p>
                </a>
              </li>

            </ul>
          </li>

        </ul>
      </nav>
      <!-- Removed duplicate copyright -->
    </div>
</aside>

<!-- SweetAlert2 for logout confirmation -->
<script src="plugins/sweetalert2/sweetalert2.all.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Logout button confirmation
  var logoutBtn = document.getElementById('logoutBtn');
  if(logoutBtn) {
    logoutBtn.addEventListener('click', function(e) {
      e.preventDefault();
      Swal.fire({
        title: 'Logout?',
        text: 'Are you sure you want to logout?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, logout!',
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = 'logout.php';
        }
      });
    });
  }

  // Ensure menu items with active children stay open
  var menuItems = document.querySelectorAll('.nav-item.menu-open');
  menuItems.forEach(function(item) {
    // Make sure parent menu is open
    item.classList.add('menu-open');

    // Make sure parent link is active
    var parentLink = item.querySelector('.nav-link');
    if (parentLink) {
      parentLink.classList.add('active');
    }
  });

  // Auto-close sidebar on mobile when clicking menu items
  var navLinks = document.querySelectorAll('.nav-sidebar .nav-link:not([data-widget="pushmenu"])');
  var bodyElement = document.querySelector('body');

  navLinks.forEach(function(link) {
    link.addEventListener('click', function(e) {
      // Don't close sidebar when clicking on dropdown toggles
      if (link.querySelector('.fa-angle-left')) {
        // This is a dropdown toggle, don't close sidebar
        return;
      }

      // Only auto-close on mobile devices
      if (window.innerWidth < 992 && bodyElement.classList.contains('sidebar-open')) {
        // Simulate click on the sidebar toggle button
        document.querySelector('[data-widget="pushmenu"]').click();
      }
    });
  });

  // Also close sidebar when clicking anywhere in the content area
  document.querySelector('.content-wrapper').addEventListener('click', function() {
    if (window.innerWidth < 992 && bodyElement.classList.contains('sidebar-open')) {
      document.querySelector('[data-widget="pushmenu"]').click();
    }
  });
});
</script>

<!--Styling for the status indicator and sidebar fixes-->
<style>
/* Remove sidebar scrollbar */
.main-sidebar {
    height: 100vh !important;
    min-height: 100% !important;
    position: fixed !important;
    overflow: hidden !important;
}

.sidebar {
    padding-bottom: 0 !important;
    overflow: hidden !important;
}

/* Make sidebar more readable */
.nav-sidebar .nav-item {
    margin-bottom: 0.2rem !important;
}

.nav-sidebar .nav-link {
    padding: 0.7rem 1rem !important;
    font-size: 1.05rem !important;
}

.nav-treeview .nav-item {
    font-size: 1rem !important;
}

.nav-treeview .nav-link {
    padding: 0.6rem 1rem 0.6rem 2.5rem !important;
}

.nav-header {
    padding: 0.7rem 1rem 0.7rem 1rem !important;
    font-size: 1.1rem !important;
}

/* Ensure menu stays open */
.nav-item.menu-open > .nav-treeview {
    display: block !important;
}

.nav-item.menu-open > .nav-link .fa-angle-left {
    transform: rotate(-90deg) !important;
}

/* Status indicator styles */
.status-container {
    position: relative;
    padding: 2px 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

#statusIndicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    transition: all 0.3s ease;
}

#statusIndicator.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

#statusIndicator.offline {
    background: #dc3545;
    animation: none;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
        box-shadow: 0 0 0 4px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}
</style>

<script>
function checkConnection() {
    // First check if browser reports we're online
    if (navigator.onLine) {
        // Make a lightweight request to check actual connection
        fetch('get-booking-details.php', {
            method: 'HEAD',
            cache: 'no-cache',
            timeout: 3000 // 3 second timeout
        })
        .then(response => {
            if (response.ok) {
                updateOnlineStatus(true);
            } else {
                // If response not ok but we're online, still show as online
                updateOnlineStatus(true);
            }
        })
        .catch(() => {
            // If fetch fails but navigator.onLine is true, still show as online
            updateOnlineStatus(true);
        });
    } else {
        updateOnlineStatus(false);
    }
}

function updateOnlineStatus(isOnline) {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');

    if (isOnline) {
        statusIndicator.className = 'status-dot online';
        statusText.textContent = 'Online';
        statusText.style.color = '#28a745';
    } else {
        statusIndicator.className = 'status-dot offline';
        statusText.textContent = 'Offline';
        statusText.style.color = '#dc3545';
    }
}

// Check connection status every 10 seconds
setInterval(checkConnection, 10000);

// Initial check
checkConnection();

// Listen for online/offline events
window.addEventListener('online', () => {
    updateOnlineStatus(true);
});

window.addEventListener('offline', () => {
    updateOnlineStatus(false);
});

// Additional check when page becomes visible
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        checkConnection();
    }
});
</script>