<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Define admin system constants
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}

include('../includes/config.php');

// Check if admin is logged in
if(strlen($_SESSION['aid'])==0) {
    header('HTTP/1.1 403 Forbidden');
    echo 'Access denied. Please log in.';
    exit;
}

// Get parameters
$booking_id = isset($_GET['booking_id']) ? intval($_GET['booking_id']) : 0;
$filter_date = isset($_GET['date']) ? $_GET['date'] : '';
$filter_booking = isset($_GET['booking']) ? $_GET['booking'] : '';
$filter_status = isset($_GET['status']) ? $_GET['status'] : '';

try {
    if ($booking_id > 0) {
        // Export specific booking manifest
        exportSingleBookingManifest($con, $booking_id);
    } else {
        // Export filtered manifests
        exportFilteredManifests($con, $filter_date, $filter_booking, $filter_status);
    }
} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo 'Error generating export: ' . $e->getMessage();
}

function exportSingleBookingManifest($con, $booking_id) {
    // Get booking details
    $booking_query = "SELECT 
        b.*,
        bt.name as boat_name,
        bt.capacity as boat_capacity,
        bt.type as boat_type
    FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    WHERE b.booking_id = ?";
    
    $stmt = $con->prepare($booking_query);
    $stmt->bind_param('i', $booking_id);
    $stmt->execute();
    $booking_result = $stmt->get_result();
    
    if ($booking_result->num_rows == 0) {
        throw new Exception('Booking not found');
    }
    
    $booking = $booking_result->fetch_assoc();
    
    // Get passenger manifest
    $manifest_query = "SELECT * FROM passenger_manifest 
                      WHERE booking_id = ? 
                      ORDER BY passenger_number ASC";
    
    $stmt = $con->prepare($manifest_query);
    $stmt->bind_param('i', $booking_id);
    $stmt->execute();
    $manifest_result = $stmt->get_result();
    
    // Generate filename
    $filename = 'passenger_manifest_' . $booking['booking_code'] . '_' . date('Y-m-d') . '.csv';
    
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create output stream
    $output = fopen('php://output', 'w');
    
    // Add header information
    fputcsv($output, ['PASSENGER MANIFEST - CARLES TOURISM OFFICE']);
    fputcsv($output, ['Booking Code:', $booking['booking_code']]);
    fputcsv($output, ['Tour Date:', date('F d, Y', strtotime($booking['start_date']))]);
    fputcsv($output, ['Destination:', $booking['tour_destination']]);
    fputcsv($output, ['Boat:', $booking['boat_name'] ?? 'Not Assigned']);
    fputcsv($output, ['Boat Capacity:', $booking['boat_capacity'] ?? 'N/A']);
    fputcsv($output, ['Total Passengers:', $booking['no_of_pax']]);
    fputcsv($output, ['Export Date:', date('F d, Y H:i:s')]);
    fputcsv($output, ['']); // Empty line
    
    // Add safety status
    $capacity = $booking['boat_capacity'] ?? 25;
    $passengers = $booking['no_of_pax'];
    $safety_status = $passengers <= $capacity ? 'WITHIN LIMITS' : 'OVERLOAD WARNING';
    fputcsv($output, ['Safety Status:', $safety_status]);
    fputcsv($output, ['']); // Empty line
    
    // Add column headers
    fputcsv($output, [
        'Passenger No.',
        'Full Name',
        'Age',
        'Gender',
        'City',
        'Province',
        'Contact Number',
        'Address',
        'Role'
    ]);
    
    // Add passenger data
    if ($manifest_result->num_rows > 0) {
        while ($passenger = $manifest_result->fetch_assoc()) {
            $role = $passenger['passenger_type'] == 'main_booker' ? 'Main Contact' : 'Passenger';
            
            fputcsv($output, [
                $passenger['passenger_number'],
                $passenger['first_name'] . ' ' . $passenger['last_name'],
                $passenger['age'],
                $passenger['sex'],
                $passenger['city'],
                $passenger['province'],
                $passenger['contact_number'] ?? 'N/A',
                $passenger['address'] ?? 'N/A',
                $role
            ]);
        }
    } else {
        // Fallback for bookings without manifest data
        fputcsv($output, [
            '1',
            $booking['first_name'] . ' ' . $booking['last_name'],
            $booking['age'],
            $booking['sex'],
            'N/A',
            'N/A',
            $booking['contact_number'],
            $booking['address'],
            'Main Contact'
        ]);
        
        // Add note about missing data
        for ($i = 2; $i <= $booking['no_of_pax']; $i++) {
            fputcsv($output, [
                $i,
                'PASSENGER DATA NOT AVAILABLE',
                'N/A',
                'N/A',
                'N/A',
                'N/A',
                'N/A',
                'N/A',
                'Passenger'
            ]);
        }
    }
    
    // Add footer
    fputcsv($output, ['']); // Empty line
    fputcsv($output, ['GOVERNMENT COMPLIANCE NOTICE:']);
    fputcsv($output, ['This passenger manifest is required by the Tourism Office,']);
    fputcsv($output, ['Marina, and Coast Guard for safety and emergency response purposes.']);
    fputcsv($output, ['All information is collected in compliance with maritime safety regulations.']);
    
    fclose($output);
}

function exportFilteredManifests($con, $filter_date, $filter_booking, $filter_status) {
    // Build query for filtered results
    $where_conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($filter_date)) {
        $where_conditions[] = "DATE(b.start_date) = ?";
        $params[] = $filter_date;
        $types .= 's';
    }
    
    if (!empty($filter_booking)) {
        $where_conditions[] = "b.booking_code LIKE ?";
        $params[] = '%' . $filter_booking . '%';
        $types .= 's';
    }
    
    if (!empty($filter_status)) {
        $where_conditions[] = "b.booking_status = ?";
        $params[] = $filter_status;
        $types .= 's';
    } else {
        $where_conditions[] = "b.booking_status IN ('confirmed', 'accepted')";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Query to get all matching manifests
    $query = "SELECT 
        b.booking_code,
        b.start_date,
        b.tour_destination,
        bt.name as boat_name,
        bt.capacity as boat_capacity,
        pm.passenger_number,
        pm.passenger_type,
        CONCAT(pm.first_name, ' ', pm.last_name) as full_name,
        pm.age,
        pm.sex,
        pm.city,
        pm.province,
        pm.contact_number,
        pm.address
    FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN passenger_manifest pm ON b.booking_id = pm.booking_id
    $where_clause
    ORDER BY b.start_date, b.booking_code, pm.passenger_number";
    
    $stmt = $con->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Generate filename
    $date_suffix = !empty($filter_date) ? $filter_date : date('Y-m-d');
    $filename = 'passenger_manifests_' . $date_suffix . '.csv';
    
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create output stream
    $output = fopen('php://output', 'w');
    
    // Add header information
    fputcsv($output, ['PASSENGER MANIFESTS - CARLES TOURISM OFFICE']);
    fputcsv($output, ['Export Date:', date('F d, Y H:i:s')]);
    if (!empty($filter_date)) {
        fputcsv($output, ['Tour Date Filter:', date('F d, Y', strtotime($filter_date))]);
    }
    if (!empty($filter_booking)) {
        fputcsv($output, ['Booking Filter:', $filter_booking]);
    }
    if (!empty($filter_status)) {
        fputcsv($output, ['Status Filter:', ucfirst($filter_status)]);
    }
    fputcsv($output, ['']); // Empty line
    
    // Add column headers
    fputcsv($output, [
        'Booking Code',
        'Tour Date',
        'Destination',
        'Boat',
        'Passenger No.',
        'Full Name',
        'Age',
        'Gender',
        'City',
        'Province',
        'Contact',
        'Role'
    ]);
    
    // Add data
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $role = $row['passenger_type'] == 'main_booker' ? 'Main Contact' : 'Passenger';
            
            fputcsv($output, [
                $row['booking_code'],
                date('M d, Y', strtotime($row['start_date'])),
                $row['tour_destination'],
                $row['boat_name'] ?? 'Not Assigned',
                $row['passenger_number'],
                $row['full_name'],
                $row['age'],
                $row['sex'],
                $row['city'],
                $row['province'],
                $row['contact_number'] ?? 'N/A',
                $role
            ]);
        }
    } else {
        fputcsv($output, ['No passenger manifest data found for the selected criteria.']);
    }
    
    // Add footer
    fputcsv($output, ['']); // Empty line
    fputcsv($output, ['GOVERNMENT COMPLIANCE NOTICE:']);
    fputcsv($output, ['This passenger manifest is required by the Tourism Office,']);
    fputcsv($output, ['Marina, and Coast Guard for safety and emergency response purposes.']);
    
    fclose($output);
}
?>
