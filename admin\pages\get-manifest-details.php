<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Define admin system constants
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}

include('../includes/config.php');

// Check if admin is logged in
if(strlen($_SESSION['aid'])==0) {
    echo '<div class="alert alert-danger">Access denied. Please log in.</div>';
    exit;
}

$booking_id = isset($_GET['booking_id']) ? intval($_GET['booking_id']) : 0;

if ($booking_id <= 0) {
    echo '<div class="alert alert-danger">Invalid booking ID.</div>';
    exit;
}

try {
    // Get booking details
    $booking_query = "SELECT 
        b.*,
        bt.name as boat_name,
        bt.capacity as boat_capacity,
        bt.type as boat_type
    FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    WHERE b.booking_id = ?";
    
    $stmt = $con->prepare($booking_query);
    $stmt->bind_param('i', $booking_id);
    $stmt->execute();
    $booking_result = $stmt->get_result();
    
    if ($booking_result->num_rows == 0) {
        echo '<div class="alert alert-danger">Booking not found.</div>';
        exit;
    }
    
    $booking = $booking_result->fetch_assoc();
    
    // Get passenger manifest
    $manifest_query = "SELECT * FROM passenger_manifest 
                      WHERE booking_id = ? 
                      ORDER BY passenger_number ASC";
    
    $stmt = $con->prepare($manifest_query);
    $stmt->bind_param('i', $booking_id);
    $stmt->execute();
    $manifest_result = $stmt->get_result();
    
    // Display booking header
    echo '<div class="container-fluid">';
    echo '<div class="row mb-3">';
    echo '<div class="col-md-6">';
    echo '<h5><i class="fas fa-ship"></i> Booking Information</h5>';
    echo '<table class="table table-sm table-borderless">';
    echo '<tr><td><strong>Booking Code:</strong></td><td>' . htmlspecialchars($booking['booking_code']) . '</td></tr>';
    echo '<tr><td><strong>Tour Date:</strong></td><td>' . date('F d, Y', strtotime($booking['start_date'])) . '</td></tr>';
    echo '<tr><td><strong>Destination:</strong></td><td>' . htmlspecialchars($booking['tour_destination']) . '</td></tr>';
    echo '<tr><td><strong>Status:</strong></td><td><span class="badge badge-primary">' . ucfirst($booking['booking_status']) . '</span></td></tr>';
    echo '</table>';
    echo '</div>';
    echo '<div class="col-md-6">';
    echo '<h5><i class="fas fa-anchor"></i> Boat Information</h5>';
    echo '<table class="table table-sm table-borderless">';
    echo '<tr><td><strong>Boat:</strong></td><td>' . htmlspecialchars($booking['boat_name'] ?? 'Not Assigned') . '</td></tr>';
    echo '<tr><td><strong>Type:</strong></td><td>' . htmlspecialchars($booking['boat_type'] ?? 'N/A') . '</td></tr>';
    echo '<tr><td><strong>Capacity:</strong></td><td>' . ($booking['boat_capacity'] ?? 'N/A') . ' passengers</td></tr>';
    echo '<tr><td><strong>Passengers:</strong></td><td>' . $booking['no_of_pax'] . ' / ' . ($booking['boat_capacity'] ?? 'N/A') . '</td></tr>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
    // Safety status
    $capacity = $booking['boat_capacity'] ?? 25;
    $passengers = $booking['no_of_pax'];
    $safety_status = $passengers <= $capacity ? 'SAFE' : 'OVERLOAD WARNING';
    $safety_class = $passengers <= $capacity ? 'success' : 'danger';
    
    echo '<div class="alert alert-' . $safety_class . ' text-center">';
    echo '<h6><i class="fas fa-life-ring"></i> Safety Status: <strong>' . $safety_status . '</strong></h6>';
    echo 'Passengers: ' . $passengers . ' | Boat Capacity: ' . $capacity;
    echo '</div>';
    
    // Passenger manifest table
    echo '<h5><i class="fas fa-users"></i> Passenger Manifest</h5>';
    
    if ($manifest_result->num_rows > 0) {
        echo '<div class="table-responsive">';
        echo '<table class="table table-bordered table-striped table-sm">';
        echo '<thead class="thead-dark">';
        echo '<tr>';
        echo '<th style="width: 50px;">No.</th>';
        echo '<th>Full Name</th>';
        echo '<th>Age</th>';
        echo '<th>Gender</th>';
        echo '<th>City</th>';
        echo '<th>Province</th>';
        echo '<th>Contact</th>';
        echo '<th>Role</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        while ($passenger = $manifest_result->fetch_assoc()) {
            $role_badge = $passenger['passenger_type'] == 'main_booker' ? 
                '<span class="badge badge-primary">Main Contact</span>' : 
                '<span class="badge badge-secondary">Passenger</span>';
            
            echo '<tr>';
            echo '<td class="text-center"><strong>' . $passenger['passenger_number'] . '</strong></td>';
            echo '<td><strong>' . htmlspecialchars($passenger['first_name'] . ' ' . $passenger['last_name']) . '</strong></td>';
            echo '<td class="text-center">' . $passenger['age'] . '</td>';
            echo '<td class="text-center">' . htmlspecialchars($passenger['sex']) . '</td>';
            echo '<td>' . htmlspecialchars($passenger['city']) . '</td>';
            echo '<td>' . htmlspecialchars($passenger['province']) . '</td>';
            echo '<td>' . htmlspecialchars($passenger['contact_number'] ?? 'N/A') . '</td>';
            echo '<td>' . $role_badge . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        
        // Summary
        echo '<div class="row mt-3">';
        echo '<div class="col-md-12">';
        echo '<div class="alert alert-info">';
        echo '<h6><i class="fas fa-info-circle"></i> Government Compliance Summary</h6>';
        echo '<div class="row">';
        echo '<div class="col-md-3"><strong>Total Passengers:</strong> ' . $manifest_result->num_rows . '</div>';
        echo '<div class="col-md-3"><strong>Boat Capacity:</strong> ' . $capacity . '</div>';
        echo '<div class="col-md-3"><strong>Status:</strong> ' . $safety_status . '</div>';
        echo '<div class="col-md-3"><strong>Compliance:</strong> <span class="badge badge-success">Complete</span></div>';
        echo '</div>';
        echo '<small class="text-muted">This manifest meets Tourism Office, Marina, and Coast Guard requirements for passenger safety and emergency response.</small>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
    } else {
        // No manifest data - show warning
        echo '<div class="alert alert-warning">';
        echo '<h6><i class="fas fa-exclamation-triangle"></i> No Passenger Manifest Data</h6>';
        echo '<p>This booking does not have detailed passenger manifest information. This may be an older booking created before the passenger manifest system was implemented.</p>';
        
        // Show basic booking info as fallback
        echo '<h6>Basic Passenger Information:</h6>';
        echo '<table class="table table-sm">';
        echo '<tr><td><strong>Main Contact:</strong></td><td>' . htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']) . '</td></tr>';
        echo '<tr><td><strong>Age:</strong></td><td>' . $booking['age'] . '</td></tr>';
        echo '<tr><td><strong>Gender:</strong></td><td>' . htmlspecialchars($booking['sex']) . '</td></tr>';
        echo '<tr><td><strong>Contact:</strong></td><td>' . htmlspecialchars($booking['contact_number']) . '</td></tr>';
        echo '<tr><td><strong>Total Passengers:</strong></td><td>' . $booking['no_of_pax'] . '</td></tr>';
        echo '</table>';
        
        echo '<div class="alert alert-danger mt-2">';
        echo '<strong>Government Compliance Warning:</strong> This booking lacks detailed passenger manifest required by Tourism Office, Marina, and Coast Guard regulations.';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>'; // Close container
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-triangle"></i> Error loading manifest details: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>
