<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Define admin system constants
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}

include('../includes/config.php');

// Check if admin is logged in
if(strlen($_SESSION['aid'])==0) {
    echo '<div class="alert alert-danger">Access denied. Please log in.</div>';
    exit;
}

// Get filter parameters
$filter_date = isset($_POST['date']) ? $_POST['date'] : '';
$filter_booking = isset($_POST['booking']) ? $_POST['booking'] : '';
$filter_status = isset($_POST['status']) ? $_POST['status'] : '';

try {
    // First, ensure the passenger_manifest table exists
    $check_table = "SHOW TABLES LIKE 'passenger_manifest'";
    $table_result = $con->query($check_table);
    
    if ($table_result->num_rows == 0) {
        // Create the table if it doesn't exist
        $create_table = "CREATE TABLE IF NOT EXISTS `passenger_manifest` (
            `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
            `booking_id` int(11) NOT NULL,
            `passenger_number` int(11) NOT NULL,
            `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `age` int(11) NOT NULL,
            `sex` enum('Male','Female','Other') NOT NULL,
            `city` varchar(100) NOT NULL,
            `province` varchar(100) NOT NULL,
            `contact_number` varchar(20) DEFAULT NULL,
            `address` varchar(255) DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT current_timestamp(),
            `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`manifest_id`),
            KEY `idx_booking_id` (`booking_id`),
            KEY `idx_passenger_type` (`passenger_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        $con->query($create_table);
    }
    
    // Build the query based on filters
    $where_conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($filter_date)) {
        $where_conditions[] = "DATE(b.start_date) = ?";
        $params[] = $filter_date;
        $types .= 's';
    }
    
    if (!empty($filter_booking)) {
        $where_conditions[] = "b.booking_code LIKE ?";
        $params[] = '%' . $filter_booking . '%';
        $types .= 's';
    }
    
    if (!empty($filter_status)) {
        $where_conditions[] = "b.booking_status = ?";
        $params[] = $filter_status;
        $types .= 's';
    }
    
    // Default to confirmed and accepted bookings if no status filter
    if (empty($filter_status)) {
        $where_conditions[] = "b.booking_status IN ('confirmed', 'accepted')";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Query to get bookings with passenger manifest
    $query = "SELECT DISTINCT
        b.booking_id,
        b.booking_code,
        b.first_name as main_first_name,
        b.last_name as main_last_name,
        b.start_date,
        b.end_date,
        b.tour_destination,
        b.no_of_pax,
        b.booking_status,
        bt.name as boat_name,
        bt.capacity as boat_capacity
    FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN passenger_manifest pm ON b.booking_id = pm.booking_id
    $where_clause
    ORDER BY b.start_date DESC, b.booking_code ASC";
    
    $stmt = $con->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo '<div class="table-responsive">';
        echo '<table id="manifestTable" class="table table-bordered table-striped">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Booking Code</th>';
        echo '<th>Tour Date</th>';
        echo '<th>Destination</th>';
        echo '<th>Main Contact</th>';
        echo '<th>Total Passengers</th>';
        echo '<th>Boat</th>';
        echo '<th>Status</th>';
        echo '<th>Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        while ($row = $result->fetch_assoc()) {
            $status_class = '';
            switch($row['booking_status']) {
                case 'confirmed': $status_class = 'badge-success'; break;
                case 'accepted': $status_class = 'badge-primary'; break;
                case 'pending': $status_class = 'badge-warning'; break;
                default: $status_class = 'badge-secondary';
            }
            
            echo '<tr>';
            echo '<td><strong>' . htmlspecialchars($row['booking_code']) . '</strong></td>';
            echo '<td>' . date('M d, Y', strtotime($row['start_date'])) . '</td>';
            echo '<td>' . htmlspecialchars($row['tour_destination']) . '</td>';
            echo '<td>' . htmlspecialchars($row['main_first_name'] . ' ' . $row['main_last_name']) . '</td>';
            echo '<td><span class="badge badge-info">' . $row['no_of_pax'] . ' passengers</span></td>';
            echo '<td>' . htmlspecialchars($row['boat_name'] ?? 'Not Assigned') . '</td>';
            echo '<td><span class="badge ' . $status_class . '">' . ucfirst($row['booking_status']) . '</span></td>';
            echo '<td>';
            echo '<button class="btn btn-sm btn-primary" onclick="viewManifestDetails(' . $row['booking_id'] . ')">';
            echo '<i class="fas fa-eye"></i> View Details';
            echo '</button> ';
            echo '<button class="btn btn-sm btn-success" onclick="exportBookingManifest(' . $row['booking_id'] . ')">';
            echo '<i class="fas fa-download"></i> Export';
            echo '</button>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        
        // Add JavaScript for actions
        echo '<script>';
        echo 'function viewManifestDetails(bookingId) {';
        echo '    // Load manifest details in modal or new window';
        echo '    fetch("get-manifest-details.php?booking_id=" + bookingId)';
        echo '        .then(response => response.text())';
        echo '        .then(data => {';
        echo '            Swal.fire({';
        echo '                title: "Passenger Manifest Details",';
        echo '                html: data,';
        echo '                width: "90%",';
        echo '                showCloseButton: true,';
        echo '                showConfirmButton: false,';
        echo '                customClass: {';
        echo '                    popup: "swal-wide"';
        echo '                }';
        echo '            });';
        echo '        })';
        echo '        .catch(error => {';
        echo '            Swal.fire("Error", "Failed to load manifest details", "error");';
        echo '        });';
        echo '}';
        echo 'function exportBookingManifest(bookingId) {';
        echo '    window.open("export-passenger-manifest.php?booking_id=" + bookingId, "_blank");';
        echo '}';
        echo '</script>';

        // Add CSS for wide modal
        echo '<style>';
        echo '.swal-wide { max-width: 95% !important; }';
        echo '.swal2-html-container { max-height: 70vh; overflow-y: auto; }';
        echo '</style>';
        
    } else {
        echo '<div class="alert alert-info">';
        echo '<i class="fas fa-info-circle"></i> No passenger manifests found for the selected criteria.';
        if (empty($filter_date) && empty($filter_booking)) {
            echo '<br><small>Try selecting a specific date or entering a booking code.</small>';
        }
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-triangle"></i> Error loading passenger manifests: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>
