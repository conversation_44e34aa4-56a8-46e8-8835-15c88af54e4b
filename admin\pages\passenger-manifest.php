<?php session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Define admin system constants (only if not already defined)
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}
if (!defined('ADMIN_BASE_URL')) {
    define('ADMIN_BASE_URL', '/Online Booking Reservation System/admin/');
}

include('../includes/config.php');

if(strlen($_SESSION['aid'])==0) {
    header('location:' . ADMIN_BASE_URL . 'pages/login.php');
} else {
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System | Passenger Manifest</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
  <?php include_once("../includes/navbar.php");?>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
  <?php include_once("../includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1><i class="fas fa-users"></i> Passenger Manifest</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">Passenger Manifest</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        
        <!-- Info Alert -->
        <div class="alert alert-info">
          <h5><i class="icon fas fa-info"></i> Government Compliance Notice</h5>
          This passenger manifest system is required by the Tourism Office, Marina, and Coast Guard for safety and emergency response purposes. All passenger information is collected for official maritime safety protocols.
        </div>

        <!-- Filter Section -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title"><i class="fas fa-filter"></i> Filter Passenger Manifests</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <label for="filterDate">Tour Date:</label>
                <input type="date" id="filterDate" class="form-control" value="<?php echo date('Y-m-d'); ?>">
              </div>
              <div class="col-md-3">
                <label for="filterBooking">Booking Code:</label>
                <input type="text" id="filterBooking" class="form-control" placeholder="Enter booking code">
              </div>
              <div class="col-md-3">
                <label for="filterStatus">Booking Status:</label>
                <select id="filterStatus" class="form-control">
                  <option value="">All Statuses</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="accepted">Accepted</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
              <div class="col-md-3">
                <label>&nbsp;</label><br>
                <button type="button" class="btn btn-primary" onclick="loadManifests()">
                  <i class="fas fa-search"></i> Search
                </button>
                <button type="button" class="btn btn-success" onclick="exportManifest()">
                  <i class="fas fa-download"></i> Export
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Passenger Manifest Table -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title"><i class="fas fa-list"></i> Passenger Manifest Records</h3>
          </div>
          <div class="card-body">
            <div id="manifestContainer">
              <div class="text-center">
                <i class="fas fa-search fa-3x text-muted"></i>
                <p class="text-muted mt-2">Use the filters above to search for passenger manifests</p>
              </div>
            </div>
          </div>
        </div>

      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?php include_once("../includes/footer.php");?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- SweetAlert2 -->
<script src="plugins/sweetalert2/sweetalert2.min.js"></script>

<script>
// Load passenger manifests based on filters
function loadManifests() {
    const filterDate = document.getElementById('filterDate').value;
    const filterBooking = document.getElementById('filterBooking').value;
    const filterStatus = document.getElementById('filterStatus').value;
    
    if (!filterDate && !filterBooking) {
        Swal.fire({
            title: 'Filter Required',
            text: 'Please select a date or enter a booking code to search.',
            icon: 'warning'
        });
        return;
    }
    
    // Show loading
    document.getElementById('manifestContainer').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-3x text-primary"></i>
            <p class="text-muted mt-2">Loading passenger manifests...</p>
        </div>
    `;
    
    // Make AJAX request to load manifests
    fetch('get-passenger-manifest.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `date=${encodeURIComponent(filterDate)}&booking=${encodeURIComponent(filterBooking)}&status=${encodeURIComponent(filterStatus)}`
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('manifestContainer').innerHTML = data;
        
        // Initialize DataTable if table exists
        if (document.getElementById('manifestTable')) {
            $('#manifestTable').DataTable({
                "responsive": true,
                "lengthChange": false,
                "autoWidth": false,
                "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                "pageLength": 25,
                "order": [[0, "asc"], [1, "asc"]]
            }).buttons().container().appendTo('#manifestTable_wrapper .col-md-6:eq(0)');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('manifestContainer').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> Error loading passenger manifests. Please try again.
            </div>
        `;
    });
}

// Export manifest for government reporting
function exportManifest() {
    const filterDate = document.getElementById('filterDate').value;
    const filterBooking = document.getElementById('filterBooking').value;
    const filterStatus = document.getElementById('filterStatus').value;
    
    if (!filterDate && !filterBooking) {
        Swal.fire({
            title: 'Filter Required',
            text: 'Please select a date or enter a booking code to export.',
            icon: 'warning'
        });
        return;
    }
    
    // Create export URL
    const exportUrl = `export-passenger-manifest.php?date=${encodeURIComponent(filterDate)}&booking=${encodeURIComponent(filterBooking)}&status=${encodeURIComponent(filterStatus)}`;
    
    // Open export in new window
    window.open(exportUrl, '_blank');
}

// Load today's manifests on page load
document.addEventListener('DOMContentLoaded', function() {
    // Auto-load today's manifests
    loadManifests();
});
</script>

</body>
</html>
<?php } ?>
