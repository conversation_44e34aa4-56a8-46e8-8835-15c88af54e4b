-- Migration script to add passenger_manifest table
-- This script adds the passenger manifest functionality to existing booking systems
-- Required for government compliance (Coast Guard, Marina, Tourism Office)

-- Check if passenger_manifest table exists, if not create it
CREATE TABLE IF NOT EXISTS `passenger_manifest` (
  `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `passenger_number` int(11) NOT NULL,
  `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` enum('Male','Female','Other') NOT NULL,
  `city` varchar(100) NOT NULL,
  `province` varchar(100) NOT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`manifest_id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_passenger_type` (`passenger_type`),
  CONSTRAINT `fk_passenger_manifest_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add stored procedure to get passenger manifest for a booking
DELIMITER $$

DROP PROCEDURE IF EXISTS `GetPassengerManifest` $$
CREATE PROCEDURE `GetPassengerManifest` (IN `p_booking_id` INT)
BEGIN
    SELECT 
        pm.manifest_id,
        pm.booking_id,
        pm.passenger_number,
        pm.passenger_type,
        pm.first_name,
        pm.last_name,
        pm.age,
        pm.sex,
        pm.city,
        pm.province,
        pm.contact_number,
        pm.address,
        pm.created_at,
        b.booking_code,
        b.start_date,
        b.tour_destination,
        b.no_of_pax
    FROM passenger_manifest pm
    JOIN bookings b ON pm.booking_id = b.booking_id
    WHERE pm.booking_id = p_booking_id
    ORDER BY pm.passenger_number ASC;
END$$

-- Add stored procedure to get complete booking with manifest
DROP PROCEDURE IF EXISTS `GetBookingWithManifest` $$
CREATE PROCEDURE `GetBookingWithManifest` (IN `p_booking_id` INT)
BEGIN
    -- Get booking details
    SELECT 
        b.*,
        bt.name as boat_name,
        bt.capacity as boat_capacity
    FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    WHERE b.booking_id = p_booking_id;
    
    -- Get passenger manifest
    SELECT 
        pm.*
    FROM passenger_manifest pm
    WHERE pm.booking_id = p_booking_id
    ORDER BY pm.passenger_number ASC;
END$$

-- Add stored procedure to export passenger manifest for government reporting
DROP PROCEDURE IF EXISTS `ExportPassengerManifestForDate` $$
CREATE PROCEDURE `ExportPassengerManifestForDate` (IN `p_date` DATE)
BEGIN
    SELECT 
        b.booking_code,
        b.start_date as tour_date,
        b.tour_destination,
        bt.name as boat_name,
        bt.capacity as boat_capacity,
        b.no_of_pax as total_passengers,
        pm.passenger_number,
        pm.passenger_type,
        CONCAT(pm.first_name, ' ', pm.last_name) as full_name,
        pm.age,
        pm.sex,
        pm.city,
        pm.province,
        pm.contact_number,
        pm.address,
        CASE 
            WHEN pm.passenger_type = 'main_booker' THEN 'Main Contact'
            ELSE 'Passenger'
        END as role
    FROM bookings b
    JOIN passenger_manifest pm ON b.booking_id = pm.booking_id
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    WHERE DATE(b.start_date) = p_date
    AND b.booking_status IN ('confirmed', 'accepted')
    ORDER BY b.booking_code, pm.passenger_number ASC;
END$$

DELIMITER ;

-- Insert sample data for testing (optional - remove in production)
-- This will only run if there are existing bookings
INSERT IGNORE INTO `passenger_manifest` 
(`booking_id`, `passenger_number`, `passenger_type`, `first_name`, `last_name`, `age`, `sex`, `city`, `province`, `contact_number`, `address`)
SELECT 
    b.booking_id,
    1 as passenger_number,
    'main_booker' as passenger_type,
    b.first_name,
    b.last_name,
    b.age,
    CASE 
        WHEN LOWER(b.sex) = 'male' THEN 'Male'
        WHEN LOWER(b.sex) = 'female' THEN 'Female'
        ELSE 'Other'
    END as sex,
    COALESCE(SUBSTRING_INDEX(b.address, ',', 1), 'Unknown') as city,
    COALESCE(SUBSTRING_INDEX(b.address, ',', -1), 'Unknown') as province,
    b.contact_number,
    b.address
FROM bookings b
WHERE b.booking_id NOT IN (SELECT DISTINCT booking_id FROM passenger_manifest WHERE passenger_type = 'main_booker')
LIMIT 10; -- Limit to prevent overwhelming the system

-- Create index for better performance
CREATE INDEX IF NOT EXISTS `idx_passenger_manifest_booking_passenger` ON `passenger_manifest` (`booking_id`, `passenger_number`);
CREATE INDEX IF NOT EXISTS `idx_passenger_manifest_name` ON `passenger_manifest` (`first_name`, `last_name`);

-- Success message
SELECT 'Passenger manifest table and procedures created successfully!' as status;
