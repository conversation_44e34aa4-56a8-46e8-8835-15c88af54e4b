-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 06, 2025 at 12:01 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.1.25

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `booking_system`
--

--
-- Procedures
--
DELIMITER $$
DROP PROCEDURE IF EXISTS `CheckBoatAvailabilityForDate` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `CheckBoatAvailabilityForDate` (IN `p_date` DATE)
BEGIN
    SELECT
        b.boat_id,
        b.name,
        b.type,
        b.capacity,
        b.price_per_day,
        -- Check specific date availability first, then fall back to general status
        COALESCE(bad.status, b.availability_status) AS availability_status,
        CASE
            WHEN bad.status IS NOT NULL THEN 'Specific date status'
            ELSE 'General status'
        END AS status_source,
        bad.notes,
        -- Check if boat is already booked
        CASE
            WHEN bk.boat_id IS NOT NULL THEN 'Booked'
            ELSE 'Not Booked'
        END AS booking_status
    FROM
        boats b
    -- Check specific date availability
    LEFT JOIN
        boat_availability_dates bad ON b.boat_id = bad.boat_id AND bad.available_date = p_date
    -- Check if there are bookings for this date
    LEFT JOIN (
        SELECT DISTINCT boat_id
        FROM bookings
        WHERE DATE(start_date) = p_date
    ) bk ON b.boat_id = bk.boat_id
    ORDER BY
        b.name;
END$$

DROP PROCEDURE IF EXISTS `GenerateMonthlyTouristReport` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `GenerateMonthlyTouristReport` (IN `report_month` INT, IN `report_year` INT, IN `admin_id` INT)   BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE report_id_var INT;
    DECLARE most_visited VARCHAR(255);
    DECLARE total_tourists_var INT DEFAULT 0;
    DECLARE regular_tourists_var INT DEFAULT 0;
    DECLARE discounted_tourists_var INT DEFAULT 0;
    DECLARE children_tourists_var INT DEFAULT 0;
    DECLARE infants_tourists_var INT DEFAULT 0;
    DECLARE total_revenue_var DECIMAL(10,2) DEFAULT 0;
    DECLARE env_fee_var DECIMAL(10,2) DEFAULT 0;

    -- Set date range for the report
    SET start_date = CONCAT(report_year, '-', LPAD(report_month, 2, '0'), '-01');
    SET end_date = LAST_DAY(start_date);

    -- Find the most visited destination for this period
    SELECT d.name INTO most_visited
    FROM bookings b
    JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.start_date BETWEEN start_date AND end_date
      AND b.booking_status = 'confirmed'
    GROUP BY d.name
    ORDER BY COUNT(*) DESC
    LIMIT 1;

    -- Calculate totals
    SELECT
        SUM(no_of_pax) AS total_tourists,
        SUM(regular_pax) AS regular_tourists,
        SUM(discounted_pax) AS discounted_tourists,
        SUM(children_pax) AS children_tourists,
        SUM(infants_pax) AS infants_tourists,
        SUM(total) AS total_revenue,
        SUM(environmental_fee) AS env_fee
    INTO
        total_tourists_var,
        regular_tourists_var,
        discounted_tourists_var,
        children_tourists_var,
        infants_tourists_var,
        total_revenue_var,
        env_fee_var
    FROM bookings
    WHERE start_date BETWEEN start_date AND end_date
      AND booking_status = 'confirmed';

    -- Insert the report
    INSERT INTO tourist_reports (
        report_date,
        report_type,
        total_tourists,
        regular_tourists,
        discounted_tourists,
        children_tourists,
        infants_tourists,
        total_revenue,
        environmental_fee_collected,
        most_visited_destination,
        generated_by
    ) VALUES (
        end_date,
        'monthly',
        IFNULL(total_tourists_var, 0),
        IFNULL(regular_tourists_var, 0),
        IFNULL(discounted_tourists_var, 0),
        IFNULL(children_tourists_var, 0),
        IFNULL(infants_tourists_var, 0),
        IFNULL(total_revenue_var, 0),
        IFNULL(env_fee_var, 0),
        most_visited,
        admin_id
    );

    -- Get the inserted report ID
    SET report_id_var = LAST_INSERT_ID();

    -- Insert destination details
    INSERT INTO tourist_report_details (
        report_id,
        destination_id,
        destination_name,
        tourist_count,
        revenue
    )
    SELECT
        report_id_var,
        d.destination_id,
        d.name,
        SUM(b.no_of_pax),
        SUM(b.total)
    FROM bookings b
    JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.start_date BETWEEN start_date AND end_date
      AND b.booking_status = 'confirmed'
    GROUP BY d.destination_id, d.name;

    -- Insert origin statistics
    INSERT INTO tourist_origin_stats (
        report_id,
        origin_location,
        tourist_count,
        percentage
    )
    SELECT
        report_id_var,
        SUBSTRING_INDEX(address, ',', -2) AS origin,
        SUM(no_of_pax) AS count,
        (SUM(no_of_pax) / total_tourists_var) * 100 AS percentage
    FROM bookings
    WHERE start_date BETWEEN start_date AND end_date
      AND booking_status = 'confirmed'
    GROUP BY origin
    ORDER BY count DESC;

    -- Return the report ID
    SELECT report_id_var AS report_id;
END$$

DROP PROCEDURE IF EXISTS `GetAvailableBoats` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetAvailableBoats` (IN `p_date` DATE)   BEGIN
    -- Get all boats
    SELECT
        b.boat_id,
        b.name,
        b.type,
        b.capacity,
        b.price_per_day,
        b.status,
        b.availability_status,
        -- Check if boat is already scheduled for the given date
        CASE WHEN bl.boat IS NULL THEN 'Available' ELSE 'Scheduled' END AS schedule_status
    FROM
        boats b
    LEFT JOIN (
        -- Get boats scheduled for the given date
        SELECT DISTINCT boat
        FROM booking_logs bl
        JOIN tourist t ON bl.user_id = t.user_id
        WHERE DATE(t.date_of_tour) = p_date
    ) bl ON b.name = bl.boat
    WHERE
        b.availability_status = 'available'
    ORDER BY
        b.name;
END$$

DROP PROCEDURE IF EXISTS `GetBoatSchedule` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `GetBoatSchedule` (IN `p_start_date` DATE, IN `p_end_date` DATE)   BEGIN
    SELECT
        bl.log_id,
        bl.date_of_booking,
        bl.boat,
        t.full_name AS tourist_name,
        t.mobile_number,
        t.email_address,
        t.date_of_tour,
        t.number_of_pax,
        t.tour_destination,
        bl.price,
        a.username AS admin_name
    FROM
        booking_logs bl
    JOIN
        tourist t ON bl.user_id = t.user_id
    LEFT JOIN
        admins a ON bl.admin_id = a.admin_id
    WHERE
        t.date_of_tour BETWEEN p_start_date AND p_end_date
    ORDER BY
        t.date_of_tour, bl.boat;
END$$

DROP PROCEDURE IF EXISTS `MarkBoatAvailableForDate` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `MarkBoatAvailableForDate` (IN `p_boat_id` INT, IN `p_date` DATE, IN `p_status` ENUM('available','not available','maintenance'), IN `p_notes` TEXT, IN `p_admin_id` INT)   BEGIN
    INSERT INTO boat_availability_dates (boat_id, available_date, status, notes, added_by)
    VALUES (p_boat_id, p_date, p_status, p_notes, p_admin_id)
    ON DUPLICATE KEY UPDATE
        status = p_status,
        notes = p_notes,
        added_by = p_admin_id,
        updated_at = CURRENT_TIMESTAMP();

    -- Log the action
    INSERT INTO activity_logs (admin_id, activity, activity_time)
    VALUES (p_admin_id, CONCAT('Updated boat ID ', p_boat_id, ' availability for ', p_date, ' to ', p_status), CURRENT_TIMESTAMP());
END$$

DROP PROCEDURE IF EXISTS `MarkBoatAvailableForDateRange` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `MarkBoatAvailableForDateRange` (IN `p_boat_id` INT, IN `p_start_date` DATE, IN `p_end_date` DATE, IN `p_status` ENUM('available','not available','maintenance'), IN `p_notes` TEXT, IN `p_admin_id` INT)   BEGIN
    DECLARE v_current_date DATE;
    SET v_current_date = p_start_date;

    -- Loop through each date in the range
    WHILE v_current_date <= p_end_date DO
        -- Call the single date procedure
        CALL MarkBoatAvailableForDate(p_boat_id, v_current_date, p_status, p_notes, p_admin_id);

        -- Move to next day
        SET v_current_date = DATE_ADD(v_current_date, INTERVAL 1 DAY);
    END WHILE;

    -- Log the action
    INSERT INTO activity_logs (admin_id, activity, activity_time)
    VALUES (p_admin_id, CONCAT('Updated boat ID ', p_boat_id, ' availability for date range ', p_start_date, ' to ', p_end_date), CURRENT_TIMESTAMP());
END$$

DROP PROCEDURE IF EXISTS `PopulateBookingLogs` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `PopulateBookingLogs` ()   BEGIN
    -- First ensure we have tourist records for all bookings
    INSERT IGNORE INTO `tourist` (`full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`)
    SELECT
        CONCAT(b.first_name, ' ', b.last_name),
        b.address,
        b.contact_number,
        b.email,
        b.sex,
        CURDATE() - INTERVAL b.age YEAR, -- Approximate birthdate based on age
        DATE(b.start_date), -- Tour date from booking
        b.no_of_pax,
        b.tour_destination
    FROM
        bookings b
    WHERE
        b.email IS NOT NULL;

    -- Now populate booking_logs
    INSERT IGNORE INTO `booking_logs` (`user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`)
    SELECT
        t.user_id,
        DATE(b.created_at),
        TIME(b.created_at),
        b.booking_id,
        bt.name,
        b.total,
        1 -- Default admin_id
    FROM
        bookings b
    JOIN
        boats bt ON b.boat_id = bt.boat_id
    JOIN
        tourist t ON t.email_address = b.email
    WHERE
        b.email IS NOT NULL;
END$$

DROP PROCEDURE IF EXISTS `ScheduleBoat` $$
CREATE DEFINER=`root`@`localhost` PROCEDURE `ScheduleBoat` (IN `p_tourist_name` VARCHAR(100), IN `p_tourist_address` VARCHAR(255), IN `p_tourist_mobile` VARCHAR(20), IN `p_tourist_email` VARCHAR(100), IN `p_tourist_sex` ENUM('Male','Female','Other'), IN `p_tourist_birthdate` DATE, IN `p_tour_date` DATE, IN `p_number_of_pax` INT, IN `p_tour_destination` VARCHAR(255), IN `p_boat_name` VARCHAR(100), IN `p_price` DECIMAL(10,2), IN `p_admin_id` INT)   BEGIN
    DECLARE v_user_id INT;
    DECLARE v_log_id INT;

    -- Start transaction
    START TRANSACTION;

    -- Check if tourist already exists
    SELECT user_id INTO v_user_id FROM tourist
    WHERE email_address = p_tourist_email LIMIT 1;

    -- If tourist doesn't exist, create a new one
    IF v_user_id IS NULL THEN
        INSERT INTO tourist (
            full_name,
            address,
            mobile_number,
            email_address,
            sex,
            birthdate,
            date_of_tour,
            number_of_pax,
            tour_destination
        ) VALUES (
            p_tourist_name,
            p_tourist_address,
            p_tourist_mobile,
            p_tourist_email,
            p_tourist_sex,
            p_tourist_birthdate,
            p_tour_date,
            p_number_of_pax,
            p_tour_destination
        );

        SET v_user_id = LAST_INSERT_ID();
    ELSE
        -- Update existing tourist with new tour information
        UPDATE tourist SET
            date_of_tour = p_tour_date,
            number_of_pax = p_number_of_pax,
            tour_destination = p_tour_destination
        WHERE user_id = v_user_id;
    END IF;

    -- Create booking log entry
    INSERT INTO booking_logs (
        user_id,
        date_of_booking,
        time,
        booking_id,
        boat,
        price,
        admin_id
    ) VALUES (
        v_user_id,
        CURDATE(),
        CURTIME(),
        FLOOR(1000000 + RAND() * 9000000), -- Generate a random booking ID
        p_boat_name,
        p_price,
        p_admin_id
    );

    SET v_log_id = LAST_INSERT_ID();

    -- Commit transaction
    COMMIT;

    -- Return the created IDs
    SELECT v_user_id AS tourist_id, v_log_id AS booking_log_id;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `activity` varchar(255) NOT NULL,
  `activity_time` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT IGNORE INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES
(1, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(3, 3, 'Added new boat', '2025-05-01 05:46:00'),
(4, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(6, 3, 'Added new boat', '2025-05-01 05:46:00'),
(7, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(9, 3, 'Added new boat', '2025-05-01 05:46:00'),
(10, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(12, 3, 'Added new boat', '2025-05-01 05:46:00'),
(13, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(15, 3, 'Added new boat', '2025-05-01 05:46:00'),
(16, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(18, 3, 'Added new boat', '2025-05-01 05:46:00'),
(19, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(21, 3, 'Added new boat', '2025-05-01 05:46:00'),
(22, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(24, 3, 'Added new boat', '2025-05-01 05:46:00'),
(25, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(27, 3, 'Added new boat', '2025-05-01 05:46:00'),
(28, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(30, 3, 'Added new boat', '2025-05-01 05:46:00'),
(31, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(33, 3, 'Added new boat', '2025-05-01 05:46:00'),
(34, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(36, 3, 'Added new boat', '2025-05-01 05:46:00'),
(37, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(39, 3, 'Added new boat', '2025-05-01 05:46:00'),
(40, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(41, 2, 'Updated booking status', '2025-05-01 05:46:00'),
(42, 3, 'Added new boat', '2025-05-01 05:46:00');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE IF NOT EXISTS `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT IGNORE INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Admin', '<EMAIL>', '09123456789', 'super_admin', 'active', '2025-04-28 17:28:04', '2025-05-06 05:51:53', '2025-04-14 14:37:35', '2025-04-28 17:28:04'),
(2, 'jhona', '$2y$10$abcdefghijABCDEFGHIJklmnopqrstuvwx/yz1234567890', 'Jhona Mae', 'Santander', '<EMAIL>', '09173456789', 'sub_admin', 'active', '2025-04-27 09:00:00', NULL, '2025-04-01 11:00:00', '2025-04-27 09:00:00'),
(3, 'jordan', '$2y$10$klmnopqrstuvwxABCDEFGHIJabcdefghij/yz1234567890', 'Jordan', 'Barcarlos', '<EMAIL>', '09183456789', 'sub_admin', 'active', '2025-04-25 14:00:00', NULL, '2025-04-03 13:00:00', '2025-04-25 14:00:00'),
(4, 'lhance', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lhance', 'Montero', '<EMAIL>', '09123456789', 'sub_admin', 'active', '2025-04-28 18:00:00', NULL, '2025-04-14 15:00:00', '2025-04-28 18:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `boats`
--

CREATE TABLE IF NOT EXISTS `boats` (
  `boat_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT 'small',
  `capacity` int(11) DEFAULT 10,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Available',
  `destination` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT 'available',
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`boat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boats`
--

-- Option 1: Use INSERT IGNORE to skip duplicate entries
INSERT IGNORE INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES
(1, 'Boat 1', 'small', 10, 2000.00, 'Description for Boat 1', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(2, 'Boat 2', 'medium', 15, 3500.00, 'Description for Boat 2', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(3, 'Boat 3', 'large', 20, 5000.00, 'Description for Boat 3', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(4, 'Boat 4', 'special', 10, 6000.00, 'Description for Boat 4', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');

-- --------------------------------------------------------

--
-- Stand-in structure for view `boat_availability_calendar`
-- (See below for the actual view)
--
CREATE TABLE `boat_availability_calendar` (
  `boat_id` int(11) NOT NULL,
  `boat_name` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `price_per_day` decimal(10,2) NOT NULL,
  `general_status` varchar(20) DEFAULT NULL,
  `general_availability` enum('available','not available','maintenance') DEFAULT NULL,
  `available_date` date DEFAULT NULL,
  `date_specific_status` enum('available','not available','maintenance') DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
);

-- --------------------------------------------------------

--
-- Table structure for table `boat_availability_dates`
--

CREATE TABLE `boat_availability_dates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `boat_id` int(11) NOT NULL,
  `available_date` date NOT NULL,
  `status` enum('available','not available','maintenance') NOT NULL DEFAULT 'available',
  `notes` text DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `boat_date` (`boat_id`,`available_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `boat_availability_view`
-- (See below for the actual view)
--
CREATE TABLE `boat_availability_view` (
`boat_id` int(11)
,`boat_name` varchar(255)
,`type` varchar(50)
,`capacity` int(11)
,`price_per_day` decimal(10,2)
,`status` varchar(20)
,`availability_status` enum('available','not available','maintenance')
,`scheduled_dates` longtext
);

-- --------------------------------------------------------

--
-- Table structure for table `boat_reservations`
--

CREATE TABLE `boat_reservations` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `booking_code` varchar(50) NOT NULL,
  `booking_date` date NOT NULL,
  `start_time` time NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`booking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boat_reservations`
--

INSERT IGNORE INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES
(1, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(2, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(3, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(4, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(5, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(6, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(7, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(8, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(9, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(10, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(11, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(12, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(13, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(14, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(15, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(16, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(17, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(18, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(19, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(20, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(21, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(22, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(23, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(24, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(25, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(26, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(27, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(28, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(29, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(30, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(31, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(32, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(33, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(34, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(35, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(36, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(37, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(38, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(39, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(40, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(41, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(42, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(43, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(44, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(45, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(46, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(47, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(48, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(49, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(50, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(51, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(52, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(53, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(54, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(55, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(56, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(57, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(58, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(59, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(60, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(61, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(62, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(63, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(64, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(65, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(66, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(67, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(68, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(69, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(70, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(71, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(72, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(73, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(74, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(75, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(76, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(77, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(78, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(79, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(80, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(81, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(82, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(83, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(84, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(85, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(86, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(87, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(88, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(89, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(90, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(91, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(92, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(93, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(94, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(95, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00');


-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(20) DEFAULT NULL,
  `boat_id` int(11) NOT NULL,
  `no_of_pax` int(11) NOT NULL,
  `regular_pax` int(11) NOT NULL DEFAULT 0,
  `discounted_pax` int(11) NOT NULL DEFAULT 0,
  `children_pax` int(11) NOT NULL DEFAULT 0,
  `infants_pax` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `booking_time` datetime NOT NULL,
  `environmental_fee` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `total` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `tour_destination` varchar(255) DEFAULT NULL,
  `drop_off_location` varchar(255) DEFAULT NULL,
  `booking_code` varchar(50) NOT NULL,
  `destination_id` int(11) NOT NULL,
  PRIMARY KEY (`booking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT IGNORE INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES
(1, 1, 'Ralph', 'Ramos', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Maria Ramos', '09123456789', 1, 25, 17, 3, 2, 3, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1815.00, 'manual', 7815.00, 'pending', '2025-05-03 05:51:53', 'Tumaquin Island', 'Carles Port', 'BOAT1-20250109-68532', 2),
(2, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Juan Santos', '09987654321', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'gcash', 3000.00, 'confirmed', '2025-04-28 05:51:53', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', 6),
(3, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', 3, 8, 5, 1, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 3200.00, 'gcash', 2000.00, 'pending', '2025-05-03 05:51:53', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', 4),
(4, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Pedro Santos', '09222333444', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'gcash', 1080.00, 'confirmed', '2025-04-28 05:51:53', 'Bayas Island', 'Carles Main Port', 'BOAT2-20250224-88888', 3),
(5, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Mary Doe', '09333444555', 1, 3, 2, 0, 0, 1, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1500.00, 'manual', 1080.00, 'pending', '2025-05-03 05:51:53', 'Cabugao Gamay', 'Balasan Pier', 'BOAT3-20250303-77777', 1),
(6, 1, 'Jhona Mae', 'Santander', 21, 'female', '09345797658', '<EMAIL>', 'Sicogon Island,Carles, Iloilo', 'Rico Santander', '09444555666', 1, 25, 17, 3, 2, 3, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 1815.00, 'manual', 815.00, 'confirmed', '2025-04-28 05:51:53', 'Antonia Beach', 'Sicogon Port', 'BOAT1-20250311-68532', 3),
(7, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Carlos Santos', '09555666777', 2, 4, 2, 0, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 2000.00, 'gcash', 4200.00, 'pending', '2025-05-03 05:51:53', 'Tangke Lagoon', 'Estancia Main Port', 'BOAT2-20250314-12345', 4),
(8, 1, 'Ralph', 'Ramos', 21, 'male', '09495334604', '<EMAIL>', 'Carles, Iloilo', 'Liza Ramos', '09666777888', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'manual', 1590.00, 'confirmed', '2025-04-28 05:51:53', 'Agho Island', 'Carles Tourism Port', 'BOAT1-20250406-77777', 2),
(9, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Ana Santos', '09777888999', 3, 8, 5, 1, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 3200.00, 'manual', 1200.00, 'pending', '2025-05-03 05:51:53', 'Bantigue Sandbar', 'Carles Port Area', 'BOAT3-20250403-67890', 1),
(10, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Sarah Doe', '09888999000', 1, 3, 2, 0, 0, 1, '2025-04-26 05:51:53', '2025-04-27 05:51:53', '2025-04-24 05:51:53', 1500.00, 'gcash', 6000.00, 'cancelled', '2025-04-23 05:51:53', 'Pan de Azucar', 'Balasan Main Port', 'BOAT2-20250404-88888', 3),
(11, 1, 'Ralph', 'Ramos', 21, 'male', '0934579658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', 3, 8, 5, 1, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 3200.00, 'manual', 1400.00, 'confirmed', '2025-04-28 05:51:53', 'Bucari Highlands', 'Carles Tourism Office', 'BOAT2-20250409-12345', 1),
(12, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', 1, 25, 17, 3, 2, 3, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1815.00, 'manual', 1636.00, 'pending', '2025-05-03 05:51:53', 'San Joaquin Campo Santo', 'Estancia Port', 'BOAT2-20250114-12345', 3),
(14, 1, 'Ralph', 'Ramos', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Maria Ramos', '09123456789', 1, 25, 17, 3, 2, 3, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1815.00, 'manual', 7815.00, 'pending', '2025-05-03 05:51:53', 'Tumaquin Island', 'Carles Port', 'BOAT1-20250109-68532', 2),
(15, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Juan Santos', '09987654321', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'gcash', 3000.00, 'confirmed', '2025-04-28 05:51:53', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', 6),
(16, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', 3, 8, 5, 1, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 3200.00, 'gcash', 2000.00, 'pending', '2025-05-03 05:51:53', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', 4),
(17, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Pedro Santos', '09222333444', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'gcash', 1080.00, 'confirmed', '2025-04-28 05:51:53', 'Bayas Island', 'Carles Main Port', 'BOAT2-20250224-88888', 3),
(18, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Mary Doe', '09333444555', 1, 3, 2, 0, 0, 1, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1500.00, 'manual', 1080.00, 'pending', '2025-05-03 05:51:53', 'Cabugao Gamay', 'Balasan Pier', 'BOAT3-20250303-77777', 1),
(19, 1, 'Jhona Mae', 'Santander', 21, 'female', '09345797658', '<EMAIL>', 'Sicogon Island,Carles, Iloilo', 'Rico Santander', '09444555666', 1, 25, 17, 3, 2, 3, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 1815.00, 'manual', 815.00, 'confirmed', '2025-04-28 05:51:53', 'Antonia Beach', 'Sicogon Port', 'BOAT1-20250311-68532', 3),
(20, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Carlos Santos', '09555666777', 2, 4, 2, 0, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 2000.00, 'gcash', 4200.00, 'pending', '2025-05-03 05:51:53', 'Tangke Lagoon', 'Estancia Main Port', 'BOAT2-20250314-12345', 4),
(21, 1, 'Ralph', 'Ramos', 21, 'male', '09495334604', '<EMAIL>', 'Carles, Iloilo', 'Liza Ramos', '09666777888', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'manual', 1590.00, 'confirmed', '2025-04-28 05:51:53', 'Agho Island', 'Carles Tourism Port', 'BOAT1-20250406-77777', 2),
(22, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Ana Santos', '09777888999', 3, 8, 5, 1, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 3200.00, 'manual', 1200.00, 'pending', '2025-05-03 05:51:53', 'Bantigue Sandbar', 'Carles Port Area', 'BOAT3-20250403-67890', 1),
(23, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Sarah Doe', '09888999000', 1, 3, 2, 0, 0, 1, '2025-04-26 05:51:53', '2025-04-27 05:51:53', '2025-04-24 05:51:53', 1500.00, 'gcash', 6000.00, 'cancelled', '2025-04-23 05:51:53', 'Pan de Azucar', 'Balasan Main Port', 'BOAT2-20250404-88888', 3),
(24, 1, 'Ralph', 'Ramos', 21, 'male', '0934579658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', 3, 8, 5, 1, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 3200.00, 'manual', 1400.00, 'confirmed', '2025-04-28 05:51:53', 'Bucari Highlands', 'Carles Tourism Office', 'BOAT2-20250409-12345', 1),
(25, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', 1, 25, 17, 3, 2, 3, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1815.00, 'manual', 1636.00, 'pending', '2025-05-03 05:51:53', 'San Joaquin Campo Santo', 'Estancia Port', 'BOAT2-20250114-12345', 3);

-- --------------------------------------------------------

--
-- Table structure for table `booking_status_logs`
--

CREATE TABLE IF NOT EXISTS `booking_status_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `old_status` enum('pending','confirmed','cancelled') NOT NULL,
  `new_status` enum('pending','confirmed','cancelled') NOT NULL,
  `reason` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`log_id`),
  KEY `booking_id` (`booking_id`),
  KEY `created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `booking_logs`
--

CREATE TABLE `booking_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `date_of_booking` date NOT NULL,
  `time` time NOT NULL,
  `booking_id` varchar(50) NOT NULL,
  `boat` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `booking_logs`
--

INSERT IGNORE INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES
(1, 1, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(2, 6, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(3, 11, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(4, 16, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(5, 21, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(6, 26, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(7, 31, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(8, 36, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(9, 41, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(10, 46, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(11, 51, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(12, 56, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(13, 61, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1),
(14, 66, '2025-05-01', '05:46:00', '1', 'Boat 1', 7815.00, 1);


-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE IF NOT EXISTS `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `contact_number` varchar(20) NOT NULL,
  `address` varchar(255) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` varchar(10) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT IGNORE INTO `customers` (`customer_id`, `first_name`, `last_name`, `email`, `contact_number`, `address`, `age`, `sex`, `created_at`) VALUES
(1, 'Ralph', 'Ramos', '<EMAIL>', '09495969705', 'Poblacion, Carles, Iloilo', 21, 'Male', '2025-05-01 10:00:00'),
(2, 'Christian', 'Lopez', '<EMAIL>', '09594598488', 'Balasan, Iloilo', 25, 'Female', '2025-05-02 11:30:00'),
(3, 'John', 'Doe', '<EMAIL>', '09175553333', 'Balasan, Iloilo', 30, 'Male', '2025-05-03 09:15:00'),
(4, 'Liza', 'Dela Cruz', '<EMAIL>', '09179990888', 'Roxas City, Capiz', 28, 'Female', '2025-05-04 14:20:00'),
(5, 'Mark', 'Reyes', '<EMAIL>', '09788786766', 'Kalibo, Aklan', 35, 'Male', '2025-05-05 16:45:00');


-- --------------------------------------------------------

--
-- Table structure for table `destinations`
--

CREATE TABLE IF NOT EXISTS `destinations` (
  `destination_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`destination_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `destinations`
--

INSERT IGNORE INTO `destinations` (`destination_id`, `name`, `description`, `location`, `image_path`, `status`, `created_at`) VALUES
(1, 'Tumaquin Island', 'Beautiful island with pristine beaches', 'Carles, Iloilo', 'images/destinations/tumaquin.jpg', 'active', '2025-05-01 00:00:00'),
(2, 'Gigantes Island', 'Famous for its white sand beaches and seafood', 'Carles, Iloilo', 'images/destinations/gigantes.jpg', 'active', '2025-05-01 00:00:00'),
(3, 'Sicogon Island', 'Pristine beaches and clear waters', 'Carles, Iloilo', 'images/destinations/sicogon.jpg', 'active', '2025-05-01 00:00:00'),
(4, 'Bayas Island', 'Secluded island with beautiful coral reefs', 'Estancia, Iloilo', 'images/destinations/bayas.jpg', 'active', '2025-05-01 00:00:00'),
(5, 'Cabugao Gamay', 'Iconic small island with a viewpoint', 'Carles, Iloilo', 'images/destinations/cabugao.jpg', 'active', '2025-05-01 00:00:00'),
(6, 'Antonia Beach', 'White sand beach with crystal clear waters', 'Sicogon Island, Carles', 'images/destinations/antonia.jpg', 'active', '2025-05-01 00:00:00'),
(7, 'Tangke Lagoon', 'Natural saltwater lagoon surrounded by cliffs', 'Gigantes Sur, Carles', 'images/destinations/tangke.jpg', 'active', '2025-05-01 00:00:00'),
(8, 'Agho Island', 'Uninhabited island with powdery white sand', 'Concepcion, Iloilo', 'images/destinations/agho.jpg', 'active', '2025-05-01 00:00:00'),
(9, 'Bantigue Sandbar', 'Famous for its long stretch of white sand', 'Carles, Iloilo', 'images/destinations/bantigue.jpg', 'active', '2025-05-01 00:00:00'),
(10, 'Pan de Azucar', 'Mountain island resembling a sugar loaf', 'Concepcion, Iloilo', 'images/destinations/pandeazucar.jpg', 'active', '2025-05-01 00:00:00');



-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `email` varchar(100) NOT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  `attempt_time` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `reference_id` int(11) DEFAULT NULL,
  `status` varchar(50) DEFAULT 'unread',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`notification_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `passenger_manifest`
--

CREATE TABLE `passenger_manifest` (
  `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `passenger_number` int(11) NOT NULL,
  `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` enum('Male','Female','Other') NOT NULL,
  `city` varchar(100) NOT NULL,
  `province` varchar(100) NOT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`manifest_id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_passenger_type` (`passenger_type`),
  CONSTRAINT `fk_passenger_manifest_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tourist`
--

CREATE TABLE `tourist` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(100) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `mobile_number` varchar(20) DEFAULT NULL,
  `email_address` varchar(100) DEFAULT NULL,
  `sex` enum('Male','Female','Other') DEFAULT NULL,
  `birthdate` date DEFAULT NULL,
  `date_of_tour` date DEFAULT NULL,
  `number_of_pax` int(11) DEFAULT 1,
  `tour_destination` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist`
--

INSERT IGNORE INTO `tourist` (`user_id`, `full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`, `created_at`, `updated_at`) VALUES
(1, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(2, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(3, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(4, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(5, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(6, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(7, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(8, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(9, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(10, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(11, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(12, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(13, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(14, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(15, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(16, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(17, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(18, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(19, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(20, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(21, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(22, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(23, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(24, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(25, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(26, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(27, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(28, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(29, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(30, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(31, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(32, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(33, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(34, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(35, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(36, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(37, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(38, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(39, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(40, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(41, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(42, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(43, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(44, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(45, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(46, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(47, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(48, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(49, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(50, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(51, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(52, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(53, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(54, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(55, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(56, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(57, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(58, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(59, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(60, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(61, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(62, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(63, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(64, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(65, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(66, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(67, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(68, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(69, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(70, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(71, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(72, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(73, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(74, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(75, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(76, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(77, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(78, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(79, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(80, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(81, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(82, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(83, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(84, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(85, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(86, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(87, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(88, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(89, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(90, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(91, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(92, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(93, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(94, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(95, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(96, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(97, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(98, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(99, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(100, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(101, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(102, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(103, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(104, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(105, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(106, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(107, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(108, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(109, 'Liza Dela Cruz', 'Roxas City, Capiz', '09179990888', '<EMAIL>', 'Female', '1997-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(110, 'Mark Reyes', 'Kalibo, Aklan', '09788786766', '<EMAIL>', 'Male', '1990-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(114, 'Ralph Ramos', 'Carles, Iloilo', '09345789658', '<EMAIL>', 'Male', '2004-05-05', '2025-01-09', 25, 'Tumaquin Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(115, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-01-15', 4, 'Gigantes Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(116, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-02-10', 8, 'Sicogon Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(117, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-02-25', 4, 'Bayas Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(118, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-03-10', 3, 'Cabugao Gamay', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(119, 'Jhona Mae Santander', 'Sicogon Island,Carles, Iloilo', '09345797658', '<EMAIL>', 'Female', '2004-05-05', '2025-03-12', 25, 'Antonia Beach', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(120, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-03-15', 4, 'Tangke Lagoon', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(121, 'Ralph Ramos', 'Carles, Iloilo', '09495334604', '<EMAIL>', 'Male', '2004-05-05', '2025-04-05', 4, 'Agho Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(122, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-04-06', 8, 'Bantigue Sandbar', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(123, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-04-07', 3, 'Pan de Azucar', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(124, 'Ralph Ramos', 'Carles, Iloilo', '0934579658', '<EMAIL>', 'Male', '2004-05-05', '2025-04-08', 8, 'Bucari Highlands', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(125, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-01-01', 25, 'San Joaquin Campo Santo', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(126, 'Ralph Ramos', 'Carles, Iloilo', '09345789658', '<EMAIL>', 'Male', '2004-05-05', '2025-01-09', 25, 'Tumaquin Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(127, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-01-15', 4, 'Gigantes Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(128, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-02-10', 8, 'Sicogon Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(129, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-02-25', 4, 'Bayas Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(130, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-03-10', 3, 'Cabugao Gamay', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(131, 'Jhona Mae Santander', 'Sicogon Island,Carles, Iloilo', '09345797658', '<EMAIL>', 'Female', '2004-05-05', '2025-03-12', 25, 'Antonia Beach', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(132, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-03-15', 4, 'Tangke Lagoon', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(133, 'Ralph Ramos', 'Carles, Iloilo', '09495334604', '<EMAIL>', 'Male', '2004-05-05', '2025-04-05', 4, 'Agho Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(134, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-04-06', 8, 'Bantigue Sandbar', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(135, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-04-07', 3, 'Pan de Azucar', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(136, 'Ralph Ramos', 'Carles, Iloilo', '0934579658', '<EMAIL>', 'Male', '2004-05-05', '2025-04-08', 8, 'Bucari Highlands', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(137, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-01-01', 25, 'San Joaquin Campo Santo', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(138, 'Ralph Ramos', 'Carles, Iloilo', '09345789658', '<EMAIL>', 'Male', '2004-05-05', '2025-01-09', 25, 'Tumaquin Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(139, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-01-15', 4, 'Gigantes Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(140, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-02-10', 8, 'Sicogon Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(141, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-02-25', 4, 'Bayas Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(142, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-03-10', 3, 'Cabugao Gamay', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(143, 'Jhona Mae Santander', 'Sicogon Island,Carles, Iloilo', '09345797658', '<EMAIL>', 'Female', '2004-05-05', '2025-03-12', 25, 'Antonia Beach', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(144, 'Maria Santos', 'Carles, Iloilo', '09181234567', '<EMAIL>', 'Female', '2000-05-05', '2025-03-15', 4, 'Tangke Lagoon', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),
(145, 'Ralph Ramos', 'Carles, Iloilo', '09495334604', '<EMAIL>', 'Male', '2004-05-05', '2025-04-05', 4, 'Agho Island', '2025-05-05 21:11:27', '2025-05-05 21:11:27'),

-- --------------------------------------------------------

--
-- Table structure for table `tourist_origin_stats`
--

CREATE TABLE `tourist_origin_stats` (
  `stat_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `origin_location` varchar(255) NOT NULL,
  `tourist_count` int(11) NOT NULL DEFAULT 0,
  `percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`stat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist_origin_stats`
--

INSERT IGNORE INTO `tourist_origin_stats` (`stat_id`, `report_id`, `origin_location`, `tourist_count`, `percentage`) VALUES
(1, 1, 'Iloilo', 180, 40.00),
(2, 1, 'Manila', 90, 20.00),
(3, 1, 'Cebu', 70, 15.56),
(4, 1, 'Bacolod', 60, 13.33),
(5, 1, 'International', 50, 11.11),
(6, 2, 'Iloilo', 200, 38.46),
(7, 2, 'Manila', 110, 21.15),
(8, 2, 'Cebu', 85, 16.35),
(9, 2, 'Bacolod', 75, 14.42),
(10, 2, 'International', 50, 9.62),
(11, 3, 'Iloilo', 230, 38.33),
(12, 3, 'Manila', 130, 21.67),
(13, 3, 'Cebu', 95, 15.83),
(14, 3, 'Bacolod', 85, 14.17),
(15, 3, 'International', 60, 10.00),
(16, 4, 'Iloilo', 220, 37.93),
(17, 4, 'Manila', 125, 21.55),
(18, 4, 'Cebu', 90, 15.52),
(19, 4, 'Bacolod', 80, 13.79),
(20, 4, 'International', 65, 11.21),
(21, 1, 'Iloilo', 180, 40.00),
(22, 1, 'Manila', 90, 20.00),
(23, 1, 'Cebu', 70, 15.56),
(24, 1, 'Bacolod', 60, 13.33),
(25, 1, 'International', 50, 11.11),
(26, 2, 'Iloilo', 200, 38.46),
(27, 2, 'Manila', 110, 21.15),
(28, 2, 'Cebu', 85, 16.35),
(29, 2, 'Bacolod', 75, 14.42),
(30, 2, 'International', 50, 9.62),
(31, 3, 'Iloilo', 230, 38.33),
(32, 3, 'Manila', 130, 21.67),
(33, 3, 'Cebu', 95, 15.83),
(34, 3, 'Bacolod', 85, 14.17),
(35, 3, 'International', 60, 10.00),
(36, 4, 'Iloilo', 220, 37.93),
(37, 4, 'Manila', 125, 21.55),
(38, 4, 'Cebu', 90, 15.52),
(39, 4, 'Bacolod', 80, 13.79),
(40, 4, 'International', 65, 11.21),
(41, 1, 'Iloilo', 180, 40.00),
(42, 1, 'Manila', 90, 20.00),
(43, 1, 'Cebu', 70, 15.56),
(44, 1, 'Bacolod', 60, 13.33),
(45, 1, 'International', 50, 11.11),
(46, 2, 'Iloilo', 200, 38.46),
(47, 2, 'Manila', 110, 21.15),
(48, 2, 'Cebu', 85, 16.35),
(49, 2, 'Bacolod', 75, 14.42),
(50, 2, 'International', 50, 9.62),
(51, 3, 'Iloilo', 230, 38.33),
(52, 3, 'Manila', 130, 21.67),
(53, 3, 'Cebu', 95, 15.83),
(54, 3, 'Bacolod', 85, 14.17),
(55, 3, 'International', 60, 10.00),

-- --------------------------------------------------------

--
-- Table structure for table `tourist_reports`
--

CREATE TABLE `tourist_reports` (
  `report_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly','yearly') NOT NULL,
  `total_tourists` int(11) NOT NULL DEFAULT 0,
  `regular_tourists` int(11) NOT NULL DEFAULT 0,
  `discounted_tourists` int(11) NOT NULL DEFAULT 0,
  `children_tourists` int(11) NOT NULL DEFAULT 0,
  `infants_tourists` int(11) NOT NULL DEFAULT 0,
  `total_revenue` decimal(10,2) NOT NULL DEFAULT 0.00,
  `environmental_fee_collected` decimal(10,2) NOT NULL DEFAULT 0.00,
  `most_visited_destination` varchar(255) DEFAULT NULL,
  `generated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist_reports`
--

INSERT IGNORE INTO `tourist_reports` (`report_id`, `report_date`, `report_type`, `total_tourists`, `regular_tourists`, `discounted_tourists`, `children_tourists`, `infants_tourists`, `total_revenue`, `environmental_fee_collected`, `most_visited_destination`, `generated_by`, `created_at`, `updated_at`) VALUES
(1, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:47:14'),
(2, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:47:14'),
(3, '2025-03-31', 'monthly', 600, 400, 90, 85, 25, 300000.00, 30000.00, 'Gigantes Island', NULL, '2025-04-01 09:00:00', '2025-05-05 20:47:14'),
(4, '2025-04-30', 'monthly', 580, 380, 85, 90, 25, 290000.00, 29000.00, 'Cabugao Gamay', 3, '2025-05-01 09:00:00', '2025-05-05 20:47:14'),
(5, '2025-01-07', 'weekly', 120, 80, 20, 15, 5, 60000.00, 6000.00, 'Gigantes Island', 1, '2025-01-08 09:00:00', '2025-05-05 20:47:14'),
(6, '2025-01-14', 'weekly', 110, 75, 18, 12, 5, 55000.00, 5500.00, 'Sicogon Island', NULL, '2025-01-15 09:00:00', '2025-05-05 20:47:14'),
(7, '2025-01-21', 'weekly', 105, 70, 17, 13, 5, 52500.00, 5250.00, 'Bayas Island', 3, '2025-01-22 09:00:00', '2025-05-05 20:47:14'),
(8, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 20:47:14'),
(9, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:50:36'),
(10, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:50:36'),
(11, '2025-03-31', 'monthly', 600, 400, 90, 85, 25, 300000.00, 30000.00, 'Gigantes Island', NULL, '2025-04-01 09:00:00', '2025-05-05 20:50:36'),
(12, '2025-04-30', 'monthly', 580, 380, 85, 90, 25, 290000.00, 29000.00, 'Cabugao Gamay', 3, '2025-05-01 09:00:00', '2025-05-05 20:50:36'),
(13, '2025-01-07', 'weekly', 120, 80, 20, 15, 5, 60000.00, 6000.00, 'Gigantes Island', 1, '2025-01-08 09:00:00', '2025-05-05 20:50:36'),
(14, '2025-01-14', 'weekly', 110, 75, 18, 12, 5, 55000.00, 5500.00, 'Sicogon Island', NULL, '2025-01-15 09:00:00', '2025-05-05 20:50:36'),
(15, '2025-01-21', 'weekly', 105, 70, 17, 13, 5, 52500.00, 5250.00, 'Bayas Island', 3, '2025-01-22 09:00:00', '2025-05-05 20:50:36'),
(16, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 20:50:36'),
(17, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:50:37'),
(18, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:50:37'),
(19, '2025-03-31', 'monthly', 600, 400, 90, 85, 25, 300000.00, 30000.00, 'Gigantes Island', NULL, '2025-04-01 09:00:00', '2025-05-05 20:50:37'),
(20, '2025-04-30', 'monthly', 580, 380, 85, 90, 25, 290000.00, 29000.00, 'Cabugao Gamay', 3, '2025-05-01 09:00:00', '2025-05-05 20:50:37'),
(21, '2025-01-07', 'weekly', 120, 80, 20, 15, 5, 60000.00, 6000.00, 'Gigantes Island', 1, '2025-01-08 09:00:00', '2025-05-05 20:50:37'),
(22, '2025-01-14', 'weekly', 110, 75, 18, 12, 5, 55000.00, 5500.00, 'Sicogon Island', NULL, '2025-01-15 09:00:00', '2025-05-05 20:50:37'),
(23, '2025-01-21', 'weekly', 105, 70, 17, 13, 5, 52500.00, 5250.00, 'Bayas Island', 3, '2025-01-22 09:00:00', '2025-05-05 20:50:37'),
(24, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 20:50:37'),
(25, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:51:36'),
(26, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:51:36'),
(27, '2025-03-31', 'monthly', 600, 400, 90, 85, 25, 300000.00, 30000.00, 'Gigantes Island', NULL, '2025-04-01 09:00:00', '2025-05-05 20:51:36'),
(28, '2025-04-30', 'monthly', 580, 380, 85, 90, 25, 290000.00, 29000.00, 'Cabugao Gamay', 3, '2025-05-01 09:00:00', '2025-05-05 20:51:36'),
(29, '2025-01-07', 'weekly', 120, 80, 20, 15, 5, 60000.00, 6000.00, 'Gigantes Island', 1, '2025-01-08 09:00:00', '2025-05-05 20:51:36'),
(30, '2025-01-14', 'weekly', 110, 75, 18, 12, 5, 55000.00, 5500.00, 'Sicogon Island', NULL, '2025-01-15 09:00:00', '2025-05-05 20:51:36'),
(31, '2025-01-21', 'weekly', 105, 70, 17, 13, 5, 52500.00, 5250.00, 'Bayas Island', 3, '2025-01-22 09:00:00', '2025-05-05 20:51:36'),
(32, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 20:51:36'),
(33, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:55:18'),
(34, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:55:18'),
(35, '2025-03-31', 'monthly', 600, 400, 90, 85, 25, 300000.00, 30000.00, 'Gigantes Island', NULL, '2025-04-01 09:00:00', '2025-05-05 20:55:18'),
(36, '2025-04-30', 'monthly', 580, 380, 85, 90, 25, 290000.00, 29000.00, 'Cabugao Gamay', 3, '2025-05-01 09:00:00', '2025-05-05 20:55:18'),
(37, '2025-01-07', 'weekly', 120, 80, 20, 15, 5, 60000.00, 6000.00, 'Gigantes Island', 1, '2025-01-08 09:00:00', '2025-05-05 20:55:18'),
(38, '2025-01-14', 'weekly', 110, 75, 18, 12, 5, 55000.00, 5500.00, 'Sicogon Island', NULL, '2025-01-15 09:00:00', '2025-05-05 20:55:18'),
(39, '2025-01-21', 'weekly', 105, 70, 17, 13, 5, 52500.00, 5250.00, 'Bayas Island', 3, '2025-01-22 09:00:00', '2025-05-05 20:55:18'),
(40, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 20:55:18'),
(41, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:57:42'),
(42, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:57:42'),
(43, '2025-03-31', 'monthly', 600, 400, 90, 85, 25, 300000.00, 30000.00, 'Gigantes Island', NULL, '2025-04-01 09:00:00', '2025-05-05 20:57:42'),
(44, '2025-04-30', 'monthly', 580, 380, 85, 90, 25, 290000.00, 29000.00, 'Cabugao Gamay', 3, '2025-05-01 09:00:00', '2025-05-05 20:57:42'),
(45, '2025-01-07', 'weekly', 120, 80, 20, 15, 5, 60000.00, 6000.00, 'Gigantes Island', 1, '2025-01-08 09:00:00', '2025-05-05 20:57:42'),
(46, '2025-01-14', 'weekly', 110, 75, 18, 12, 5, 55000.00, 5500.00, 'Sicogon Island', NULL, '2025-01-15 09:00:00', '2025-05-05 20:57:42'),
(47, '2025-01-21', 'weekly', 105, 70, 17, 13, 5, 52500.00, 5250.00, 'Bayas Island', 3, '2025-01-22 09:00:00', '2025-05-05 20:57:42'),
(48, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 20:57:42'),
(49, '2025-01-31', 'monthly', 450, 300, 75, 60, 15, 225000.00, 22500.00, 'Gigantes Island', 1, '2025-02-01 09:00:00', '2025-05-05 20:59:27'),
(50, '2025-02-28', 'monthly', 520, 350, 80, 70, 20, 260000.00, 26000.00, 'Sicogon Island', 1, '2025-03-01 09:00:00', '2025-05-05 20:59:27'),
(80, '2025-01-28', 'weekly', 115, 75, 20, 15, 5, 57500.00, 5750.00, 'Gigantes Island', 1, '2025-01-29 09:00:00', '2025-05-05 21:03:40'),

-- --------------------------------------------------------

--
-- Table structure for table `tourist_report_details`
--

CREATE TABLE `tourist_report_details` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `destination_id` int(11) DEFAULT NULL,
  `destination_name` varchar(255) DEFAULT NULL,
  `tourist_count` int(11) NOT NULL DEFAULT 0,
  `revenue` decimal(10,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist_report_details`
--

INSERT IGNORE INTO `tourist_report_details` (`detail_id`, `report_id`, `destination_id`, `destination_name`, `tourist_count`, `revenue`) VALUES
(1, 1, 2, 'Gigantes Island', 150, 75000.00),
(2, 1, 3, 'Sicogon Island', 120, 60000.00),
(3, 1, 4, 'Bayas Island', 90, 45000.00),
(4, 1, 5, 'Cabugao Gamay', 90, 45000.00),
(5, 2, 2, 'Gigantes Island', 140, 70000.00),
(6, 2, 3, 'Sicogon Island', 160, 80000.00),
(7, 2, 4, 'Bayas Island', 110, 55000.00),
(8, 2, 5, 'Cabugao Gamay', 110, 55000.00),
(9, 3, 2, 'Gigantes Island', 180, 90000.00),
(10, 3, 3, 'Sicogon Island', 150, 75000.00),
(11, 3, 4, 'Bayas Island', 130, 65000.00),
(12, 3, 5, 'Cabugao Gamay', 140, 70000.00),
(13, 4, 2, 'Gigantes Island', 150, 75000.00),
(14, 4, 3, 'Sicogon Island', 140, 70000.00),
(15, 4, 4, 'Bayas Island', 120, 60000.00),
(16, 4, 5, 'Cabugao Gamay', 170, 85000.00),


-- --------------------------------------------------------

--
-- Stand-in structure for view `tourist_statistics_view`
--
CREATE TABLE `tourist_statistics_view` (
`year` int(4)
,`month` int(2)
,`destination` varchar(255)
,`booking_count` bigint(21)
,`total_tourists` decimal(32,0)
,`regular_tourists` decimal(32,0)
,`discounted_tourists` decimal(32,0)
,`children_tourists` decimal(32,0)
,`infants_tourists` decimal(32,0)
,`total_revenue` decimal(32,2)
,`environmental_fee` decimal(32,2)
);

-- --------------------------------------------------------

--
-- Structure for view `boat_availability_calendar`
--
DROP TABLE IF EXISTS `boat_availability_calendar`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `boat_availability_calendar`  AS SELECT `b`.`boat_id` AS `boat_id`, `b`.`name` AS `boat_name`, `b`.`type` AS `type`, `b`.`capacity` AS `capacity`, `b`.`price_per_day` AS `price_per_day`, `b`.`status` AS `general_status`, `b`.`availability_status` AS `general_availability`, `bad`.`available_date` AS `available_date`, `bad`.`status` AS `date_specific_status`, `bad`.`notes` AS `notes`, `a`.`username` AS `updated_by`, `bad`.`updated_at` AS `updated_at` FROM ((`boats` `b` left join `boat_availability_dates` `bad` on(`b`.`boat_id` = `bad`.`boat_id`)) left join `admins` `a` on(`bad`.`added_by` = `a`.`admin_id`)) WHERE `bad`.`available_date` >= curdate() OR `bad`.`available_date` is null ORDER BY `b`.`name` ASC, `bad`.`available_date` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `boat_availability_view`
--
DROP TABLE IF EXISTS `boat_availability_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `boat_availability_view`  AS SELECT `b`.`boat_id` AS `boat_id`, `b`.`name` AS `boat_name`, `b`.`type` AS `type`, `b`.`capacity` AS `capacity`, `b`.`price_per_day` AS `price_per_day`, `b`.`status` AS `status`, `b`.`availability_status` AS `availability_status`, coalesce(`s`.`scheduled_dates`,'None') AS `scheduled_dates` FROM (`boats` `b` left join (select `bl`.`boat` AS `boat`,group_concat(distinct `t`.`date_of_tour` order by `t`.`date_of_tour` ASC separator ', ') AS `scheduled_dates` from (`booking_logs` `bl` join `tourist` `t` on(`bl`.`user_id` = `t`.`user_id`)) where `t`.`date_of_tour` >= curdate() group by `bl`.`boat`) `s` on(`b`.`name` = `s`.`boat`)) ORDER BY `b`.`name` ASC ;

-- --------------------------------------------------------

--
-- Structure for view `tourist_statistics_view`
--
DROP TABLE IF EXISTS `tourist_statistics_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `tourist_statistics_view`  AS SELECT year(`b`.`start_date`) AS `year`, month(`b`.`start_date`) AS `month`, `d`.`name` AS `destination`, count(`b`.`booking_id`) AS `booking_count`, sum(`b`.`no_of_pax`) AS `total_tourists`, sum(`b`.`regular_pax`) AS `regular_tourists`, sum(`b`.`discounted_pax`) AS `discounted_tourists`, sum(`b`.`children_pax`) AS `children_tourists`, sum(`b`.`infants_pax`) AS `infants_tourists`, sum(`b`.`total`) AS `total_revenue`, sum(`b`.`environmental_fee`) AS `environmental_fee` FROM (`bookings` `b` join `destinations` `d` on(`b`.`destination_id` = `d`.`destination_id`)) WHERE `b`.`booking_status` = 'confirmed' GROUP BY year(`b`.`start_date`), month(`b`.`start_date`), `d`.`name` ORDER BY year(`b`.`start_date`) DESC, month(`b`.`start_date`) DESC, sum(`b`.`no_of_pax`) DESC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`);

--
-- Indexes for table `boats`
--
ALTER TABLE `boats`
  ADD PRIMARY KEY (`boat_id`);

--
-- Indexes for table `boat_availability_dates`
--
ALTER TABLE `boat_availability_dates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `boat_date` (`boat_id`,`available_date`),
  ADD KEY `boat_id` (`boat_id`),
  ADD KEY `available_date` (`available_date`),
  ADD KEY `added_by` (`added_by`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`booking_id`);

--
-- Indexes for table `booking_logs`
--
ALTER TABLE `booking_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `booking_logs_ibfk_2` (`admin_id`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ip_address` (`ip_address`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `notifications_ibfk_1` (`user_id`);

--
-- Indexes for table `tourist`
--
ALTER TABLE `tourist`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `email_address` (`email_address`);

--
-- Indexes for table `tourist_origin_stats`
--
ALTER TABLE `tourist_origin_stats`
  ADD PRIMARY KEY (`stat_id`),
  ADD KEY `report_id` (`report_id`);

--
-- Indexes for table `tourist_reports`
--
ALTER TABLE `tourist_reports`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `generated_by` (`generated_by`);

--
-- Indexes for table `tourist_report_details`
--
ALTER TABLE `tourist_report_details`
  ADD PRIMARY KEY (`detail_id`),
  ADD KEY `report_id` (`report_id`),
  ADD KEY `destination_id` (`destination_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `boats`
--
ALTER TABLE `boats`
  MODIFY `boat_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `boat_availability_dates`
--
ALTER TABLE `boat_availability_dates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=170;

--
-- AUTO_INCREMENT for table `booking_logs`
--
ALTER TABLE `booking_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27921;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tourist`
--
ALTER TABLE `tourist`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=923;

--
-- AUTO_INCREMENT for table `tourist_origin_stats`
--
ALTER TABLE `tourist_origin_stats`
  MODIFY `stat_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=281;

--
-- AUTO_INCREMENT for table `tourist_reports`
--
ALTER TABLE `tourist_reports`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=113;

--
-- AUTO_INCREMENT for table `tourist_report_details`
--
ALTER TABLE `tourist_report_details`
  MODIFY `detail_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=161;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `boat_availability_dates`
--
ALTER TABLE `boat_availability_dates`
  ADD CONSTRAINT `boat_availability_dates_ibfk_1` FOREIGN KEY (`boat_id`) REFERENCES `boats` (`boat_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `boat_availability_dates_ibfk_2` FOREIGN KEY (`added_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_logs`
--
ALTER TABLE `booking_logs`
  ADD CONSTRAINT `booking_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tourist` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_logs_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_status_logs`
--
ALTER TABLE `booking_status_logs`
  ADD CONSTRAINT `booking_status_logs_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_status_logs_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `tourist_origin_stats`
--
ALTER TABLE `tourist_origin_stats`
  ADD CONSTRAINT `tourist_origin_stats_ibfk_1` FOREIGN KEY (`report_id`) REFERENCES `tourist_reports` (`report_id`) ON DELETE CASCADE;

--
-- Constraints for table `tourist_reports`
--
ALTER TABLE `tourist_reports`
  ADD CONSTRAINT `tourist_reports_ibfk_1` FOREIGN KEY (`generated_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

-- Sample data for customers table
INSERT INTO `customers` (`customer_id`, `first_name`, `last_name`, `email`, `contact_number`, `address`, `age`, `sex`, `created_at`) VALUES
(1, 'Juan', 'Dela Cruz', '<EMAIL>', '09123456789', 'Manila, Philippines', 35, 'Male', '2025-05-01 10:00:00'),
(2, 'Maria', 'Santos', '<EMAIL>', '09234567890', 'Quezon City, Philippines', 28, 'Female', '2025-05-02 11:30:00'),
(3, 'Pedro', 'Reyes', '<EMAIL>', '09345678901', 'Cebu City, Philippines', 42, 'Male', '2025-05-03 09:15:00'),
(4, 'Ana', 'Garcia', '<EMAIL>', '09456789012', 'Davao City, Philippines', 31, 'Female', '2025-05-04 14:20:00'),
(5, 'Jose', 'Lim', '<EMAIL>', '09567890123', 'Iloilo City, Philippines', 39, 'Male', '2025-05-05 16:45:00'),
(6, 'Sofia', 'Mendoza', '<EMAIL>', '09678901234', 'Bacolod City, Philippines', 26, 'Female', '2025-05-06 08:30:00'),
(7, 'Miguel', 'Tan', '<EMAIL>', '09789012345', 'Cagayan de Oro, Philippines', 33, 'Male', '2025-05-07 12:10:00'),
(8, 'Luisa', 'Gonzales', '<EMAIL>', '09890123456', 'Baguio City, Philippines', 29, 'Female', '2025-05-08 15:25:00'),
(9, 'Roberto', 'Cruz', '<EMAIL>', '09901234567', 'Tacloban City, Philippines', 45, 'Male', '2025-05-09 10:50:00'),
(10, 'Carmela', 'Reyes', '<EMAIL>', '09012345678', 'Zamboanga City, Philippines', 37, 'Female', '2025-05-10 13:40:00');

-- Sample data for destinations if not already in the database
INSERT IGNORE INTO `destinations` (`destination_id`, `name`, `description`, `location`, `image_path`, `status`, `created_at`) VALUES
(1, 'Isla de Gigantes', 'Beautiful island with white sand beaches', 'Carles, Iloilo', 'images/destinations/gigantes.jpg', 'active', '2025-05-01 00:00:00'),
(2, 'Sicogon Island', 'Pristine beaches and clear waters', 'Carles, Iloilo', 'images/destinations/sicogon.jpg', 'active', '2025-05-01 00:00:00'),
(3, 'Bantigue Island', 'Famous for its sandbar', 'Carles, Iloilo', 'images/destinations/bantigue.jpg', 'active', '2025-05-01 00:00:00');

-- Sample data for bookings table with different statuses (pending, confirmed, cancelled)
INSERT INTO `bookings` (`booking_id`, `booking_code`, `customer_id`, `boat_id`, `destination_id`, `first_name`, `last_name`, `email`, `contact_number`, `address`, `age`, `sex`, `emergency_name`, `emergency_contact`, `dropoff_location`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `tour_destination`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`) VALUES
(1, 'BK-20250501-001', 1, 1, 1, 'Juan', 'Dela Cruz', '<EMAIL>', '09123456789', 'Manila, Philippines', 35, 'Male', 'Maria Dela Cruz', '09123456780', 'Carles Port', 5, 3, 1, 1, 0, '2025-05-15', '2025-05-16', '08:00:00', 'Isla de Gigantes', 500.00, 'gcash', 2500.00, 'pending', '2025-05-01 10:00:00'),
(2, 'BK-20250502-002', 2, 2, 2, 'Maria', 'Santos', '<EMAIL>', '09234567890', 'Quezon City, Philippines', 28, 'Female', 'Pedro Santos', '09234567891', 'Estancia Port', 4, 2, 1, 1, 0, '2025-05-16', '2025-05-17', '09:00:00', 'Sicogon Island', 400.00, 'manual', 3900.00, 'confirmed', '2025-05-02 11:30:00'),
(3, 'BK-20250503-003', 3, 3, 3, 'Pedro', 'Reyes', '<EMAIL>', '09345678901', 'Cebu City, Philippines', 42, 'Male', 'Ana Reyes', '09345678902', 'Carles Port', 6, 4, 2, 0, 0, '2025-05-17', '2025-05-18', '10:00:00', 'Bantigue Island', 600.00, 'gcash', 5600.00, 'cancelled', '2025-05-03 09:15:00'),
(4, 'BK-20250504-004', 4, 4, 1, 'Ana', 'Garcia', '<EMAIL>', '09456789012', 'Davao City, Philippines', 31, 'Female', 'Jose Garcia', '09456789013', 'Estancia Port', 3, 2, 1, 0, 0, '2025-05-18', '2025-05-19', '08:30:00', 'Isla de Gigantes', 300.00, 'manual', 6300.00, 'pending', '2025-05-04 14:20:00'),
(5, 'BK-20250505-005', 5, 1, 2, 'Jose', 'Lim', '<EMAIL>', '09567890123', 'Iloilo City, Philippines', 39, 'Male', 'Sofia Lim', '09567890124', 'Carles Port', 7, 5, 1, 1, 0, '2025-05-19', '2025-05-20', '09:30:00', 'Sicogon Island', 700.00, 'gcash', 2700.00, 'confirmed', '2025-05-05 16:45:00'),
(6, 'BK-20250506-006', 6, 2, 3, 'Sofia', 'Mendoza', '<EMAIL>', '09678901234', 'Bacolod City, Philippines', 26, 'Female', 'Miguel Mendoza', '09678901235', 'Estancia Port', 5, 3, 1, 0, 1, '2025-05-20', '2025-05-21', '10:30:00', 'Bantigue Island', 500.00, 'manual', 4000.00, 'cancelled', '2025-05-06 08:30:00'),
(7, 'BK-20250507-007', 7, 3, 1, 'Miguel', 'Tan', '<EMAIL>', '09789012345', 'Cagayan de Oro, Philippines', 33, 'Male', 'Luisa Tan', '09789012346', 'Carles Port', 8, 6, 1, 1, 0, '2025-05-21', '2025-05-22', '08:00:00', 'Isla de Gigantes', 800.00, 'gcash', 5800.00, 'pending', '2025-05-07 12:10:00'),
(8, 'BK-20250508-008', 8, 4, 2, 'Luisa', 'Gonzales', '<EMAIL>', '09890123456', 'Baguio City, Philippines', 29, 'Female', 'Roberto Gonzales', '09890123457', 'Estancia Port', 4, 2, 1, 1, 0, '2025-05-22', '2025-05-23', '09:00:00', 'Sicogon Island', 400.00, 'manual', 6400.00, 'confirmed', '2025-05-08 15:25:00'),
(9, 'BK-20250509-009', 9, 1, 3, 'Roberto', 'Cruz', '<EMAIL>', '09901234567', 'Tacloban City, Philippines', 45, 'Male', 'Carmela Cruz', '09901234568', 'Carles Port', 6, 4, 2, 0, 0, '2025-05-23', '2025-05-24', '10:00:00', 'Bantigue Island', 600.00, 'gcash', 2600.00, 'cancelled', '2025-05-09 10:50:00'),
(10, 'BK-20250510-010', 10, 2, 1, 'Carmela', 'Reyes', '<EMAIL>', '09012345678', 'Zamboanga City, Philippines', 37, 'Female', 'Juan Reyes', '09012345679', 'Estancia Port', 5, 3, 1, 1, 0, '2025-05-24', '2025-05-25', '08:30:00', 'Isla de Gigantes', 500.00, 'manual', 4000.00, 'pending', '2025-05-10 13:40:00');

-- Sample data for notifications
INSERT INTO `notifications` (`notification_id`, `user_id`, `message`, `type`, `reference_id`, `status`, `is_read`, `created_at`) VALUES
(1, 1, 'New booking received: BK-20250501-001', 'new_booking', 1, 'unread', 0, '2025-05-01 10:00:00'),
(2, 1, 'New booking received: BK-20250504-004', 'new_booking', 4, 'unread', 0, '2025-05-04 14:20:00'),
(3, 1, 'New booking received: BK-20250507-007', 'new_booking', 7, 'unread', 0, '2025-05-07 12:10:00'),
(4, 1, 'New booking received: BK-20250510-010', 'new_booking', 10, 'unread', 0, '2025-05-10 13:40:00'),
(5, 1, 'Booking BK-20250502-002 has been confirmed', 'confirmation', 2, 'unread', 0, '2025-05-02 12:00:00'),
(6, 1, 'Booking BK-20250505-005 has been confirmed', 'confirmation', 5, 'unread', 0, '2025-05-05 17:00:00'),
(7, 1, 'Booking BK-20250508-008 has been confirmed', 'confirmation', 8, 'unread', 0, '2025-05-08 16:00:00'),
(8, 1, 'Booking BK-20250503-003 has been cancelled', 'cancellation', 3, 'unread', 0, '2025-05-03 10:00:00'),
(9, 1, 'Booking BK-20250506-006 has been cancelled', 'cancellation', 6, 'unread', 0, '2025-05-06 09:00:00'),
(10, 1, 'Booking BK-20250509-009 has been cancelled', 'cancellation', 9, 'unread', 0, '2025-05-09 11:00:00');

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
