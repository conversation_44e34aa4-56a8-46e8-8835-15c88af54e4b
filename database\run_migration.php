<?php
// Migration runner for passenger manifest table
// Run this script to add the passenger_manifest table to your database

// Include database connection
require_once __DIR__ . '/../website/includes/db_connection.php';

echo "Starting passenger manifest table migration...\n";

try {
    // Read the migration SQL file
    $migration_sql = file_get_contents('add_passenger_manifest_table.sql');
    
    if ($migration_sql === false) {
        throw new Exception("Could not read migration file");
    }
    
    echo "Migration file loaded successfully.\n";
    
    // Split the SQL into individual statements
    $statements = explode(';', $migration_sql);
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements and comments
        if (empty($statement) || strpos($statement, '--') === 0 || strpos($statement, 'DELIMITER') === 0) {
            continue;
        }
        
        // Handle stored procedures (they contain multiple semicolons)
        if (strpos($statement, 'CREATE PROCEDURE') !== false || strpos($statement, 'DROP PROCEDURE') !== false) {
            // For procedures, we need to handle them differently
            continue;
        }
        
        echo "Executing: " . substr($statement, 0, 50) . "...\n";
        
        if ($conn->query($statement)) {
            $success_count++;
            echo "✓ Success\n";
        } else {
            $error_count++;
            echo "✗ Error: " . $conn->error . "\n";
        }
    }
    
    echo "\nMigration completed!\n";
    echo "Successful statements: $success_count\n";
    echo "Failed statements: $error_count\n";
    
    // Test if the table was created
    $test_query = "SHOW TABLES LIKE 'passenger_manifest'";
    $result = $conn->query($test_query);
    
    if ($result && $result->num_rows > 0) {
        echo "✓ passenger_manifest table created successfully!\n";
        
        // Show table structure
        $desc_query = "DESCRIBE passenger_manifest";
        $desc_result = $conn->query($desc_query);
        
        if ($desc_result) {
            echo "\nTable structure:\n";
            while ($row = $desc_result->fetch_assoc()) {
                echo "- {$row['Field']}: {$row['Type']}\n";
            }
        }
    } else {
        echo "✗ passenger_manifest table was not created.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

// Close connection
$conn->close();
echo "\nMigration script completed.\n";
?>
