<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passenger Manifest Sample Forms</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .form-sample {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-header {
            background: #00a8b5;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        .passenger-section {
            border: 2px solid #e0f7fa;
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8fdff;
        }
        .main-booker {
            border-color: #00a8b5;
            background: #e8f4f8;
        }
        .additional-passenger {
            border-color: #17a2b8;
            background: #f0f9ff;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 10px 0;
        }
        .form-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 10px 0;
        }
        .input-field {
            margin: 10px 0;
        }
        .input-field label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .input-field input, .input-field select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .passenger-count {
            background: #17a2b8;
            color: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin: 15px 0;
        }
        .compliance-notice {
            background: #e8f4f8;
            border-left: 4px solid #17a2b8;
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .passenger-header {
            background: #00a8b5;
            color: white;
            padding: 8px 12px;
            margin: -15px -15px 15px -15px;
            border-radius: 3px 3px 0 0;
            font-weight: bold;
        }
        .main-contact-badge {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #00a8b5;">Passenger Manifest Sample Forms</h1>
        <p style="text-align: center; color: #666;">Demonstrating the passenger manifest system for different group sizes</p>

        <!-- Sample Form 1: Solo Traveler -->
        <div class="form-sample">
            <div class="form-header">
                <h2>Sample Form 1: Solo Traveler (1 Passenger)</h2>
                <p>Booking Code: BOAT-20250719-001 | Tour Date: July 25, 2025 | Destination: Sicogon Island</p>
            </div>

            <div class="compliance-notice">
                <strong>Important:</strong> Please declare all passengers who will join the tour. This information is required by the Tourism Office, Marina, and Coast Guard for safety and emergency response purposes.
            </div>

            <div class="passenger-count">
                <strong>Total Passengers: 1</strong>
            </div>

            <div class="passenger-section main-booker">
                <div class="passenger-header">
                    Passenger #1 - Main Booker <span class="main-contact-badge">Main Contact</span>
                </div>
                
                <div class="form-grid">
                    <div class="input-field">
                        <label>First Name</label>
                        <input type="text" value="Juan" readonly>
                    </div>
                    <div class="input-field">
                        <label>Last Name</label>
                        <input type="text" value="Dela Cruz" readonly>
                    </div>
                </div>

                <div class="form-grid-3">
                    <div class="input-field">
                        <label>Age</label>
                        <input type="number" value="35" readonly>
                    </div>
                    <div class="input-field">
                        <label>Gender</label>
                        <select disabled>
                            <option value="Male" selected>Male</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label>Contact Number</label>
                        <input type="text" value="09123456789" readonly>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="input-field">
                        <label>City</label>
                        <input type="text" value="Manila" readonly>
                    </div>
                    <div class="input-field">
                        <label>Province</label>
                        <input type="text" value="NCR" readonly>
                    </div>
                </div>

                <div class="input-field">
                    <label>Complete Address</label>
                    <input type="text" value="123 Rizal Street, Barangay 1, Manila, NCR" readonly>
                </div>
            </div>

            <div style="background: #d4edda; padding: 10px; border-radius: 5px; text-align: center;">
                <strong>✅ Solo Traveler - No additional passengers to declare</strong>
            </div>
        </div>

        <!-- Sample Form 2: Small Group -->
        <div class="form-sample">
            <div class="form-header">
                <h2>Sample Form 2: Small Group (3 Passengers)</h2>
                <p>Booking Code: BOAT-20250719-002 | Tour Date: July 26, 2025 | Destination: Tumaquin Island</p>
            </div>

            <div class="compliance-notice">
                <strong>Important:</strong> Please declare all passengers who will join the tour. This information is required by the Tourism Office, Marina, and Coast Guard for safety and emergency response purposes.
            </div>

            <div class="passenger-count">
                <strong>Total Passengers: 3</strong>
            </div>

            <!-- Main Booker -->
            <div class="passenger-section main-booker">
                <div class="passenger-header">
                    Passenger #1 - Main Booker <span class="main-contact-badge">Main Contact</span>
                </div>
                
                <div class="form-grid">
                    <div class="input-field">
                        <label>First Name</label>
                        <input type="text" value="Maria" readonly>
                    </div>
                    <div class="input-field">
                        <label>Last Name</label>
                        <input type="text" value="Santos" readonly>
                    </div>
                </div>

                <div class="form-grid-3">
                    <div class="input-field">
                        <label>Age</label>
                        <input type="number" value="28" readonly>
                    </div>
                    <div class="input-field">
                        <label>Gender</label>
                        <select disabled>
                            <option value="Female" selected>Female</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label>Contact Number</label>
                        <input type="text" value="09987654321" readonly>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="input-field">
                        <label>City</label>
                        <input type="text" value="Iloilo City" readonly>
                    </div>
                    <div class="input-field">
                        <label>Province</label>
                        <input type="text" value="Iloilo" readonly>
                    </div>
                </div>

                <div class="input-field">
                    <label>Complete Address</label>
                    <input type="text" value="456 Molo Street, Molo District, Iloilo City, Iloilo" readonly>
                </div>
            </div>

            <!-- Additional Passenger 2 -->
            <div class="passenger-section additional-passenger">
                <div class="passenger-header">
                    Passenger #2 - Additional Passenger
                </div>
                
                <div class="form-grid">
                    <div class="input-field">
                        <label>First Name</label>
                        <input type="text" value="Carlos" readonly>
                    </div>
                    <div class="input-field">
                        <label>Last Name</label>
                        <input type="text" value="Santos" readonly>
                    </div>
                </div>

                <div class="form-grid-3">
                    <div class="input-field">
                        <label>Age</label>
                        <input type="number" value="30" readonly>
                    </div>
                    <div class="input-field">
                        <label>Gender</label>
                        <select disabled>
                            <option value="Male" selected>Male</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label>Contact Number</label>
                        <input type="text" value="09876543210" readonly>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="input-field">
                        <label>City</label>
                        <input type="text" value="Iloilo City" readonly>
                    </div>
                    <div class="input-field">
                        <label>Province</label>
                        <input type="text" value="Iloilo" readonly>
                    </div>
                </div>

                <div class="input-field">
                    <label>Complete Address</label>
                    <input type="text" value="456 Molo Street, Molo District, Iloilo City, Iloilo" readonly>
                </div>
            </div>

            <!-- Additional Passenger 3 -->
            <div class="passenger-section additional-passenger">
                <div class="passenger-header">
                    Passenger #3 - Additional Passenger
                </div>
                
                <div class="form-grid">
                    <div class="input-field">
                        <label>First Name</label>
                        <input type="text" value="Ana" readonly>
                    </div>
                    <div class="input-field">
                        <label>Last Name</label>
                        <input type="text" value="Santos" readonly>
                    </div>
                </div>

                <div class="form-grid-3">
                    <div class="input-field">
                        <label>Age</label>
                        <input type="number" value="8" readonly>
                    </div>
                    <div class="input-field">
                        <label>Gender</label>
                        <select disabled>
                            <option value="Female" selected>Female</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label>Contact Number</label>
                        <input type="text" value="" readonly placeholder="N/A (Child)">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="input-field">
                        <label>City</label>
                        <input type="text" value="Iloilo City" readonly>
                    </div>
                    <div class="input-field">
                        <label>Province</label>
                        <input type="text" value="Iloilo" readonly>
                    </div>
                </div>

                <div class="input-field">
                    <label>Complete Address</label>
                    <input type="text" value="456 Molo Street, Molo District, Iloilo City, Iloilo" readonly>
                </div>
            </div>

            <div style="background: #d4edda; padding: 10px; border-radius: 5px; text-align: center;">
                <strong>✅ Small Group Complete - 1 Main Contact + 2 Additional Passengers</strong>
            </div>
        </div>

        <!-- Sample Form 3: Large Group -->
        <div class="form-sample">
            <div class="form-header">
                <h2>Sample Form 3: Large Group (15 Passengers)</h2>
                <p>Booking Code: BOAT-20250719-003 | Tour Date: July 27, 2025 | Destination: Antonia Island</p>
            </div>

            <div class="compliance-notice">
                <strong>Important:</strong> Please declare all passengers who will join the tour. This information is required by the Tourism Office, Marina, and Coast Guard for safety and emergency response purposes.
            </div>

            <div class="passenger-count">
                <strong>Total Passengers: 15</strong> (Within boat capacity limit of 25)
            </div>

            <!-- Main Booker -->
            <div class="passenger-section main-booker">
                <div class="passenger-header">
                    Passenger #1 - Main Booker <span class="main-contact-badge">Main Contact</span>
                </div>

                <div class="form-grid">
                    <div class="input-field">
                        <label>First Name</label>
                        <input type="text" value="Roberto" readonly>
                    </div>
                    <div class="input-field">
                        <label>Last Name</label>
                        <input type="text" value="Garcia" readonly>
                    </div>
                </div>

                <div class="form-grid-3">
                    <div class="input-field">
                        <label>Age</label>
                        <input type="number" value="45" readonly>
                    </div>
                    <div class="input-field">
                        <label>Gender</label>
                        <select disabled>
                            <option value="Male" selected>Male</option>
                        </select>
                    </div>
                    <div class="input-field">
                        <label>Contact Number</label>
                        <input type="text" value="09123456789" readonly>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="input-field">
                        <label>City</label>
                        <input type="text" value="Carles" readonly>
                    </div>
                    <div class="input-field">
                        <label>Province</label>
                        <input type="text" value="Iloilo" readonly>
                    </div>
                </div>

                <div class="input-field">
                    <label>Complete Address</label>
                    <input type="text" value="789 Poblacion, Carles, Iloilo" readonly>
                </div>
            </div>

            <!-- Additional Passengers Table Format (Dean's Request) -->
            <div style="background: #f0f9ff; border: 2px solid #17a2b8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <div style="background: #17a2b8; color: white; padding: 8px 12px; margin: -15px -15px 15px -15px; border-radius: 3px 3px 0 0; font-weight: bold;">
                    Additional Passengers Table (14 additional passengers)
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                        <thead>
                            <tr style="background: #e8f4f8;">
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">No.</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">First Name</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">Last Name</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Age</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Gender</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">City</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">Province</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">Contact</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center; font-weight: bold;">2</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Elena</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Garcia</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">42</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">Female</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Carles</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Iloilo</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">09987654321</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center; font-weight: bold;">3</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Miguel</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Garcia</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">18</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">Male</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Carles</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Iloilo</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">09876543210</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center; font-weight: bold;">4</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Sofia</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Garcia</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">16</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">Female</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Carles</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Iloilo</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">N/A</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center; font-weight: bold;">5</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Pedro</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Reyes</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">38</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">Male</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Estancia</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Iloilo</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">09765432109</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center; font-weight: bold;">6</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Carmen</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Reyes</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">35</td>
                                <td style="border: 1px solid #ddd; padding: 5px; text-align: center;">Female</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Estancia</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">Iloilo</td>
                                <td style="border: 1px solid #ddd; padding: 5px;">09654321098</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td colspan="8" style="border: 1px solid #ddd; padding: 8px; text-align: center; font-style: italic; color: #666;">
                                    ... and 9 more passengers (Passengers #7-15) with complete details ...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin-top: 10px;">
                    <strong>📋 Dean's Table Method:</strong> User declares "15" passengers → System automatically generates table for 14 additional passengers (15 total - 1 main booker = 14 additional)
                </div>
            </div>

            <div style="background: #d4edda; padding: 10px; border-radius: 5px; text-align: center;">
                <strong>✅ Large Group Complete - 1 Main Contact + 14 Additional Passengers = 15 Total</strong><br>
                <small>Within boat capacity limits • Government compliance ready • Emergency response prepared</small>
            </div>
        </div>

        <!-- Summary Section -->
        <div style="background: #e8f4f8; border: 2px solid #00a8b5; padding: 20px; border-radius: 8px; margin: 30px 0;">
            <h3 style="color: #00a8b5; margin-top: 0;">🎯 Passenger Manifest System Summary</h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div style="text-align: center;">
                    <h4 style="color: #28a745;">Form 1: Solo Traveler</h4>
                    <p><strong>1 Passenger</strong><br>Main booker only<br>No additional passengers</p>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: #17a2b8;">Form 2: Small Group</h4>
                    <p><strong>3 Passengers</strong><br>1 Main booker<br>2 Additional passengers</p>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: #6f42c1;">Form 3: Large Group</h4>
                    <p><strong>15 Passengers</strong><br>1 Main booker<br>14 Additional passengers</p>
                </div>
            </div>

            <div style="background: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h4 style="color: #00a8b5;">🏛️ Government Compliance Features:</h4>
                <ul style="margin: 10px 0;">
                    <li><strong>Tourism Office:</strong> Complete passenger listing with all required details</li>
                    <li><strong>Marina:</strong> Boat capacity validation and safety enforcement</li>
                    <li><strong>Coast Guard:</strong> Emergency response ready passenger manifest</li>
                    <li><strong>Safety Limits:</strong> Maximum 25 passengers per boat (strictly enforced)</li>
                </ul>
            </div>

            <div style="background: white; padding: 15px; border-radius: 5px;">
                <h4 style="color: #00a8b5;">📋 Dean's Table Method Implementation:</h4>
                <p><strong>"DIBA NAG DECLARE KA DA 15 SA NUMBER OF PAX, NAH PAG NAG DECLARE KA DA, SA DALUM SINA HO DAPAT MAY AMO NAH SYA NGA LISTA"</strong></p>
                <ul style="margin: 10px 0;">
                    <li>User declares total passengers (e.g., 15)</li>
                    <li>System automatically generates table for additional passengers (14 in this case)</li>
                    <li>Main booker information separate from additional passengers table</li>
                    <li>All data saved to database for government compliance</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>
