<?php
// Test script for passenger manifest system
// This script tests the complete workflow from database table creation to data insertion

echo "<h1>Passenger Manifest System Test</h1>\n";
echo "<p>Testing the complete passenger manifest workflow...</p>\n";

// Include database connection
require_once 'website/includes/db_connection.php';

$test_results = [];

// Test 1: Check if passenger_manifest table exists
echo "<h2>Test 1: Database Table Check</h2>\n";
try {
    $check_table = "SHOW TABLES LIKE 'passenger_manifest'";
    $result = $conn->query($check_table);
    
    if ($result->num_rows > 0) {
        echo "✅ passenger_manifest table exists<br>\n";
        $test_results['table_exists'] = true;
        
        // Show table structure
        $desc_query = "DESCRIBE passenger_manifest";
        $desc_result = $conn->query($desc_query);
        
        if ($desc_result) {
            echo "<strong>Table structure:</strong><br>\n";
            echo "<ul>\n";
            while ($row = $desc_result->fetch_assoc()) {
                echo "<li>{$row['Field']}: {$row['Type']}</li>\n";
            }
            echo "</ul>\n";
        }
    } else {
        echo "❌ passenger_manifest table does not exist<br>\n";
        echo "Creating table...<br>\n";
        
        // Create the table
        $create_table = "CREATE TABLE IF NOT EXISTS `passenger_manifest` (
            `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
            `booking_id` int(11) NOT NULL,
            `passenger_number` int(11) NOT NULL,
            `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `age` int(11) NOT NULL,
            `sex` enum('Male','Female','Other') NOT NULL,
            `city` varchar(100) NOT NULL,
            `province` varchar(100) NOT NULL,
            `contact_number` varchar(20) DEFAULT NULL,
            `address` varchar(255) DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT current_timestamp(),
            `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`manifest_id`),
            KEY `idx_booking_id` (`booking_id`),
            KEY `idx_passenger_type` (`passenger_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        if ($conn->query($create_table)) {
            echo "✅ passenger_manifest table created successfully<br>\n";
            $test_results['table_exists'] = true;
        } else {
            echo "❌ Failed to create passenger_manifest table: " . $conn->error . "<br>\n";
            $test_results['table_exists'] = false;
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking table: " . $e->getMessage() . "<br>\n";
    $test_results['table_exists'] = false;
}

// Test 2: Check existing bookings
echo "<h2>Test 2: Existing Bookings Check</h2>\n";
try {
    $booking_query = "SELECT COUNT(*) as total FROM bookings";
    $result = $conn->query($booking_query);
    $row = $result->fetch_assoc();
    
    echo "📊 Total bookings in system: {$row['total']}<br>\n";
    $test_results['total_bookings'] = $row['total'];
    
    if ($row['total'] > 0) {
        // Show recent bookings
        $recent_query = "SELECT booking_id, booking_code, first_name, last_name, no_of_pax, start_date 
                        FROM bookings 
                        ORDER BY created_at DESC 
                        LIMIT 5";
        $recent_result = $conn->query($recent_query);
        
        echo "<strong>Recent bookings:</strong><br>\n";
        echo "<ul>\n";
        while ($booking = $recent_result->fetch_assoc()) {
            echo "<li>#{$booking['booking_code']} - {$booking['first_name']} {$booking['last_name']} ({$booking['no_of_pax']} pax) - " . date('M d, Y', strtotime($booking['start_date'])) . "</li>\n";
        }
        echo "</ul>\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking bookings: " . $e->getMessage() . "<br>\n";
    $test_results['total_bookings'] = 0;
}

// Test 3: Check passenger manifest data
echo "<h2>Test 3: Passenger Manifest Data Check</h2>\n";
try {
    $manifest_query = "SELECT COUNT(*) as total FROM passenger_manifest";
    $result = $conn->query($manifest_query);
    $row = $result->fetch_assoc();
    
    echo "📊 Total passenger manifest records: {$row['total']}<br>\n";
    $test_results['total_manifest_records'] = $row['total'];
    
    if ($row['total'] > 0) {
        // Show sample manifest data
        $sample_query = "SELECT pm.*, b.booking_code 
                        FROM passenger_manifest pm 
                        JOIN bookings b ON pm.booking_id = b.booking_id 
                        ORDER BY pm.created_at DESC 
                        LIMIT 5";
        $sample_result = $conn->query($sample_query);
        
        echo "<strong>Sample manifest records:</strong><br>\n";
        echo "<ul>\n";
        while ($manifest = $sample_result->fetch_assoc()) {
            $role = $manifest['passenger_type'] == 'main_booker' ? 'Main Contact' : 'Passenger';
            echo "<li>#{$manifest['booking_code']} - {$manifest['first_name']} {$manifest['last_name']} ({$role}) - {$manifest['age']}y, {$manifest['sex']}</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "ℹ️ No passenger manifest records found. This is normal for a new installation.<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking passenger manifest: " . $e->getMessage() . "<br>\n";
    $test_results['total_manifest_records'] = 0;
}

// Test 4: Test admin interface files
echo "<h2>Test 4: Admin Interface Files Check</h2>\n";
$admin_files = [
    'admin/pages/passenger-manifest.php' => 'Main passenger manifest page',
    'admin/pages/get-passenger-manifest.php' => 'Manifest data fetcher',
    'admin/pages/get-manifest-details.php' => 'Manifest details viewer',
    'admin/pages/export-passenger-manifest.php' => 'Manifest export functionality'
];

foreach ($admin_files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}: {$file}<br>\n";
        $test_results['admin_files'][$file] = true;
    } else {
        echo "❌ Missing {$description}: {$file}<br>\n";
        $test_results['admin_files'][$file] = false;
    }
}

// Test 5: Test booking process integration
echo "<h2>Test 5: Booking Process Integration Check</h2>\n";
$integration_file = 'website/process/integrated_verification.php';
if (file_exists($integration_file)) {
    $content = file_get_contents($integration_file);
    if (strpos($content, 'savePassengerManifest') !== false) {
        echo "✅ Booking process includes passenger manifest saving<br>\n";
        $test_results['booking_integration'] = true;
    } else {
        echo "❌ Booking process missing passenger manifest integration<br>\n";
        $test_results['booking_integration'] = false;
    }
} else {
    echo "❌ Booking process file not found<br>\n";
    $test_results['booking_integration'] = false;
}

// Test Summary
echo "<h2>Test Summary</h2>\n";
$total_tests = 0;
$passed_tests = 0;

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>Test</th><th>Status</th><th>Details</th></tr>\n";

// Database table test
$total_tests++;
if ($test_results['table_exists']) {
    $passed_tests++;
    echo "<tr><td>Database Table</td><td style='color: green;'>✅ PASS</td><td>passenger_manifest table exists</td></tr>\n";
} else {
    echo "<tr><td>Database Table</td><td style='color: red;'>❌ FAIL</td><td>passenger_manifest table missing</td></tr>\n";
}

// Booking integration test
$total_tests++;
if ($test_results['booking_integration']) {
    $passed_tests++;
    echo "<tr><td>Booking Integration</td><td style='color: green;'>✅ PASS</td><td>Passenger manifest saving integrated</td></tr>\n";
} else {
    echo "<tr><td>Booking Integration</td><td style='color: red;'>❌ FAIL</td><td>Passenger manifest saving not integrated</td></tr>\n";
}

// Admin interface test
$total_tests++;
$admin_files_passed = array_sum($test_results['admin_files'] ?? []);
$admin_files_total = count($admin_files);
if ($admin_files_passed == $admin_files_total) {
    $passed_tests++;
    echo "<tr><td>Admin Interface</td><td style='color: green;'>✅ PASS</td><td>All {$admin_files_total} admin files present</td></tr>\n";
} else {
    echo "<tr><td>Admin Interface</td><td style='color: orange;'>⚠️ PARTIAL</td><td>{$admin_files_passed}/{$admin_files_total} admin files present</td></tr>\n";
}

echo "</table>\n";

echo "<h3>Overall Result: {$passed_tests}/{$total_tests} tests passed</h3>\n";

if ($passed_tests == $total_tests) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>\n";
    echo "<strong>🎉 SUCCESS!</strong> The passenger manifest system is fully implemented and ready for use.\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>\n";
    echo "<strong>⚠️ ATTENTION NEEDED!</strong> Some components of the passenger manifest system need attention.\n";
    echo "</div>\n";
}

echo "<h3>Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li>Test the booking form on the website to ensure passenger data is collected</li>\n";
echo "<li>Submit a test booking to verify passenger manifest data is saved</li>\n";
echo "<li>Access the admin panel and check the Passenger Manifest section</li>\n";
echo "<li>Test the export functionality for government reporting</li>\n";
echo "</ol>\n";

// Close database connection
$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
