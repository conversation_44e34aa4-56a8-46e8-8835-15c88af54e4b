<?php
// Integrated verification process for Carles Tourism Booking System
// This file handles booking saving, email verification, and admin notifications in one process

// Include database connection
require_once '../config/db_connect.php';

// Include PHPMailer classes
use PHPMailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Include PHPMailer files
require_once 'phpmailer/src/Exception.php';
require_once 'phpmailer/src/PHPMailer.php';
require_once 'phpmailer/src/SMTP.php';

// Function to log debug information
function debug_log($message, $data = null) {
    $log_file = 'logs/integrated_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);

    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0777, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message";

    if ($data !== null) {
        $log_message .= ': ' . print_r($data, true);
    }

    $log_message .= PHP_EOL;

    file_put_contents($log_file, $log_message, FILE_APPEND);
}

// Function to save passenger manifest data
function savePassengerManifest($con, $booking_id, $booking_data) {
    try {
        debug_log('Starting passenger manifest save for booking ID', $booking_id);

        // First, ensure the passenger_manifest table exists
        $create_table_query = "CREATE TABLE IF NOT EXISTS `passenger_manifest` (
            `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
            `booking_id` int(11) NOT NULL,
            `passenger_number` int(11) NOT NULL,
            `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `age` int(11) NOT NULL,
            `sex` enum('Male','Female','Other') NOT NULL,
            `city` varchar(100) NOT NULL,
            `province` varchar(100) NOT NULL,
            `contact_number` varchar(20) DEFAULT NULL,
            `address` varchar(255) DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT current_timestamp(),
            `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`manifest_id`),
            KEY `idx_booking_id` (`booking_id`),
            KEY `idx_passenger_type` (`passenger_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $con->query($create_table_query);
        debug_log('Passenger manifest table ensured');

        // Save main booker as passenger #1
        $main_first_name = mysqli_real_escape_string($con, $booking_data['firstName'] ?? '');
        $main_last_name = mysqli_real_escape_string($con, $booking_data['lastName'] ?? '');
        $main_age = intval($booking_data['age'] ?? 0);
        $main_sex = mysqli_real_escape_string($con, $booking_data['sex'] ?? '');
        $main_city = mysqli_real_escape_string($con, $booking_data['city'] ?? '');
        $main_province = mysqli_real_escape_string($con, $booking_data['province'] ?? '');
        $main_contact = mysqli_real_escape_string($con, $booking_data['contactNumber'] ?? '');
        $main_address = mysqli_real_escape_string($con, $booking_data['completeAddress'] ?? '');

        // Normalize sex values
        $main_sex = ucfirst(strtolower($main_sex));
        if (!in_array($main_sex, ['Male', 'Female', 'Other'])) {
            $main_sex = 'Other';
        }

        $main_booker_query = "INSERT INTO passenger_manifest (
            booking_id, passenger_number, passenger_type, first_name, last_name,
            age, sex, city, province, contact_number, address
        ) VALUES (
            $booking_id, 1, 'main_booker', '$main_first_name', '$main_last_name',
            $main_age, '$main_sex', '$main_city', '$main_province', '$main_contact', '$main_address'
        )";

        if ($con->query($main_booker_query)) {
            debug_log('Main booker saved to passenger manifest');
        } else {
            debug_log('Error saving main booker to passenger manifest', $con->error);
            return false;
        }

        // Save additional passengers if any
        $total_passengers = intval($booking_data['numberOfPax'] ?? 1);
        debug_log('Total passengers to save', $total_passengers);

        for ($i = 2; $i <= $total_passengers; $i++) {
            $passenger_first_name = mysqli_real_escape_string($con, $booking_data["passenger{$i}FirstName"] ?? '');
            $passenger_last_name = mysqli_real_escape_string($con, $booking_data["passenger{$i}LastName"] ?? '');
            $passenger_age = intval($booking_data["passenger{$i}Age"] ?? 0);
            $passenger_sex = mysqli_real_escape_string($con, $booking_data["passenger{$i}Sex"] ?? '');
            $passenger_city = mysqli_real_escape_string($con, $booking_data["passenger{$i}City"] ?? '');
            $passenger_province = mysqli_real_escape_string($con, $booking_data["passenger{$i}Province"] ?? '');
            $passenger_contact = mysqli_real_escape_string($con, $booking_data["passenger{$i}ContactNumber"] ?? '');
            $passenger_address = mysqli_real_escape_string($con, $booking_data["passenger{$i}Address"] ?? '');

            // Skip if essential data is missing
            if (empty($passenger_first_name) || empty($passenger_last_name) || $passenger_age <= 0) {
                debug_log("Skipping passenger $i due to missing essential data");
                continue;
            }

            // Normalize sex values
            $passenger_sex = ucfirst(strtolower($passenger_sex));
            if (!in_array($passenger_sex, ['Male', 'Female', 'Other'])) {
                $passenger_sex = 'Other';
            }

            $passenger_query = "INSERT INTO passenger_manifest (
                booking_id, passenger_number, passenger_type, first_name, last_name,
                age, sex, city, province, contact_number, address
            ) VALUES (
                $booking_id, $i, 'additional_passenger', '$passenger_first_name', '$passenger_last_name',
                $passenger_age, '$passenger_sex', '$passenger_city', '$passenger_province', '$passenger_contact', '$passenger_address'
            )";

            if ($con->query($passenger_query)) {
                debug_log("Additional passenger $i saved to manifest");
            } else {
                debug_log("Error saving additional passenger $i to manifest", $con->error);
            }
        }

        debug_log('Passenger manifest save completed successfully');
        return true;

    } catch (Exception $e) {
        debug_log('Exception in savePassengerManifest', $e->getMessage());
        return false;
    }
}

// Log start of script
debug_log('Integrated verification process started');

// Get booking data from POST
$booking = $_POST;

// Debug log the booking data
debug_log('Received booking data', $booking);

// Validate emergency contact data
if (empty($booking['emergencyName']) || empty($booking['emergencyNumber'])) {
    debug_log('ERROR: Missing emergency contact information');
    echo json_encode(['success' => false, 'message' => 'Emergency contact information is required']);
    exit;
}

// Sanitize emergency contact data
$booking['emergencyName'] = trim($booking['emergencyName']);
$booking['emergencyNumber'] = trim($booking['emergencyNumber']);

// Validate emergency contact name (letters, spaces, hyphens, apostrophes, periods)
if (!preg_match('/^[A-Za-z\s\-\'\.]{2,100}$/', $booking['emergencyName'])) {
    debug_log('ERROR: Invalid emergency contact name format');
    echo json_encode(['success' => false, 'message' => 'Invalid emergency contact name format']);
    exit;
}

// Validate emergency contact number (flexible format for mobile/landline)
$emergency_number_clean = preg_replace('/[^\d]/', '', $booking['emergencyNumber']);
if (strlen($emergency_number_clean) < 7 || strlen($emergency_number_clean) > 15) {
    debug_log('ERROR: Invalid emergency contact number format');
    echo json_encode(['success' => false, 'message' => 'Invalid emergency contact number format']);
    exit;
}

// Generate a unique booking code
$booking_code = 'BOAT-' . date('Ymd') . '-' . rand(10000, 99999);

// Extract booking details
$first_name = isset($booking['firstName']) ? $booking['firstName'] : '';
$last_name = isset($booking['lastName']) ? $booking['lastName'] : '';
$email = isset($booking['emailAddress']) ? $booking['emailAddress'] : '';
$contact = isset($booking['contactNumber']) ? $booking['contactNumber'] : '';
$destination = isset($booking['locationTourDestination']) ? $booking['locationTourDestination'] : '';
$drop_off = isset($booking['dropOffLocation']) ? $booking['dropOffLocation'] : '';
$pax = isset($booking['numberOfPax']) ? intval($booking['numberOfPax']) : 0;
$start_date = isset($booking['startDate']) ? $booking['startDate'] : date('Y-m-d');
$end_date = isset($booking['endDate']) ? $booking['endDate'] : date('Y-m-d');
$payment_method = isset($booking['paymentMethod']) ? $booking['paymentMethod'] : 'Manual Payment';
$total = isset($booking['total']) ? floatval($booking['total']) : 0;
$emergency_name = isset($booking['emergencyName']) ? $booking['emergencyName'] : '';
$emergency_number = isset($booking['emergencyNumber']) ? $booking['emergencyNumber'] : '';

// Insert booking into database
$query = "INSERT INTO bookings (
    booking_code, first_name, last_name, email, contact_number,
    tour_destination, drop_off_location, no_of_pax, start_date, end_date,
    booking_status, created_at, is_today_booking, payment_method, total,
    emergency_name, emergency_number
) VALUES (
    '$booking_code', '$first_name', '$last_name', '$email', '$contact',
    '$destination', '$drop_off', $pax, '$start_date', '$end_date',
    'pending', NOW(), 1, '$payment_method', $total,
    '$emergency_name', '$emergency_number'
)";

debug_log('Executing database query', $query);

try {
    // Execute query to save booking
    if ($con->query($query)) {
        $booking_id = $con->insert_id;
        debug_log('Booking saved successfully with ID', $booking_id);

        // Save passenger manifest data
        $passenger_manifest_saved = savePassengerManifest($con, $booking_id, $booking);
        if ($passenger_manifest_saved) {
            debug_log('Passenger manifest saved successfully for booking ID', $booking_id);
        } else {
            debug_log('Warning: Failed to save passenger manifest for booking ID', $booking_id);
        }
        
        // Create notification for admin
        $notif_message = "New booking #$booking_code from $first_name $last_name for $destination. " .
                        "Date: $start_date to $end_date, Passengers: $pax";
        
        $notif_sql = "INSERT INTO notifications (
            user_id, message, type, reference_id, is_read, created_at
        ) VALUES (
            1, '$notif_message', 'new_booking', $booking_id, 0, NOW()
        )";
        
        if ($con->query($notif_sql)) {
            debug_log('Admin notification created successfully');
        } else {
            debug_log('Error creating admin notification', $con->error);
        }
        
        // Send verification email
        try {
            // Create a new PHPMailer instance
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_SERVER;                  // Enable verbose debug output
            $mail->Debugoutput = function($str, $level) {
                debug_log("PHPMailer Debug: $str");
            };
            $mail->isSMTP();                                        // Send using SMTP
            $mail->Host       = 'smtp.gmail.com';                   // Set the SMTP server to send through
            $mail->SMTPAuth   = true;                               // Enable SMTP authentication
            $mail->Username   = '<EMAIL>';    // SMTP username
            $mail->Password   = 'nvdk kolr vcbb lyut';              // SMTP password (app password)
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;     // Enable explicit TLS encryption
            $mail->Port       = 587;                                // TCP port to connect to
            
            // Set timeout to avoid long waits
            $mail->Timeout    = 60;
            
            // Recipients
            $mail->setFrom('<EMAIL>', 'Carles Tourism Booking');
            $mail->addAddress($email, "$first_name $last_name");
            $mail->addReplyTo('<EMAIL>', 'Carles Tourism Support');
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = "Carles Tourism Booking Confirmation - $booking_code";
            
            // Create email body
            $mail_body = "
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd;'>
                <div style='background-color: #218c5b; color: white; padding: 15px; text-align: center;'>
                    <h2 style='margin: 0;'>BOOKING CONFIRMATION</h2>
                </div>
                
                <div style='padding: 20px;'>
                    <p>Dear <strong>$first_name $last_name</strong>,</p>
                    
                    <p>Thank you for booking with <strong>Carles Tourism</strong>. Your booking has been received and is pending confirmation.</p>
                    
                    <div style='background-color: #f5f5f5; padding: 15px; margin: 15px 0; border-left: 4px solid #218c5b;'>
                        <p><strong>Booking ID:</strong> $booking_code</p>
                        <p><strong>Destination:</strong> $destination</p>
                        <p><strong>Date:</strong> $start_date to $end_date</p>
                        <p><strong>Passengers:</strong> $pax</p>
                        <p><strong>Drop-off Location:</strong> $drop_off</p>
                        <p><strong>Payment Method:</strong> $payment_method</p>
                        <p><strong>Total Amount:</strong> ₱" . number_format($total, 2) . "</p>
                    </div>
                    
                    <p>We will review your booking and get back to you shortly. If you have any questions, please contact us at <a href='mailto:<EMAIL>'><EMAIL></a> or call us at 0945 799 3491.</p>
                    
                    <p>Thank you for choosing Carles Tourism!</p>
                </div>
                
                <div style='background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px;'>
                    <p>&copy; " . date('Y') . " Carles Tourism. All rights reserved.</p>
                </div>
            </div>
            ";
            
            $mail->Body = $mail_body;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $mail_body));
            
            // Send the email
            debug_log('Attempting to send verification email to', $email);
            
            if ($mail->send()) {
                debug_log('Verification email sent successfully');
                
                // Return success response
                echo json_encode([
                    'success' => true,
                    'message' => 'Booking saved and verification email sent successfully.',
                    'booking_code' => $booking_code,
                    'booking_id' => $booking_id
                ]);
            } else {
                debug_log('Failed to send verification email', $mail->ErrorInfo);
                
                // Try with alternative settings (SSL on port 465)
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                $mail->Port = 465;
                debug_log('Retrying with SMTPS on port 465');
                
                if ($mail->send()) {
                    debug_log('Verification email sent successfully on second attempt');
                    
                    // Return success response
                    echo json_encode([
                        'success' => true,
                        'message' => 'Booking saved and verification email sent successfully.',
                        'booking_code' => $booking_code,
                        'booking_id' => $booking_id
                    ]);
                } else {
                    debug_log('Failed to send verification email on second attempt', $mail->ErrorInfo);
                    
                    // Return partial success (booking saved but email failed)
                    echo json_encode([
                        'success' => true,
                        'message' => 'Booking saved successfully, but there was an issue sending the verification email. Please check your email later.',
                        'booking_code' => $booking_code,
                        'booking_id' => $booking_id,
                        'email_error' => $mail->ErrorInfo
                    ]);
                }
            }
        } catch (Exception $e) {
            debug_log('Exception in email sending', $e->getMessage());
            
            // Return partial success (booking saved but email failed)
            echo json_encode([
                'success' => true,
                'message' => 'Booking saved successfully, but there was an issue sending the verification email. Please check your email later.',
                'booking_code' => $booking_code,
                'booking_id' => $booking_id,
                'email_error' => $e->getMessage()
            ]);
        }
    } else {
        debug_log('Error saving booking', $con->error);
        
        // Return error response
        echo json_encode([
            'success' => false,
            'message' => 'Error saving booking: ' . $con->error
        ]);
    }
} catch (Exception $e) {
    debug_log('Exception in booking process', $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Exception in booking process: ' . $e->getMessage()
    ]);
}
?>
